import { Component, Input, OnChanges, OnInit, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotificationService } from '../../../servicios/notification.service';

export interface Event {
  id_evento?: number;
  id_personal: number;
  tipo_evento: 'CAPACITACION' | 'REUNION' | 'EVALUACION' | 'DISCIPLINARIO' | 'RECONOCIMIENTO' | 'OTRO';
  titulo: string;
  descripcion: string;
  fecha_evento: string;
  hora_inicio: string;
  hora_fin: string;
  ubicacion?: string;
  estado: 'PROGRAMADO' | 'EN_CURSO' | 'COMPLETADO' | 'CANCELADO';
  observaciones?: string;
  created_at?: string;
  updated_at?: string;
}

@Component({
  selector: 'app-personal-events',
  templateUrl: './personal-events.component.html',
  styleUrls: ['./personal-events.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class PersonalEventsComponent implements OnInit, OnChanges {
  @Input() personalId: string | null = null;
  @Input() personalData: any = null;

  // Propiedades para gestión de eventos
  eventos: Event[] = [];
  isLoading: boolean = false;
  isSaving: boolean = false;

  // Propiedades para nuevo evento
  nuevoEvento: Event = {
    id_personal: 0,
    tipo_evento: 'CAPACITACION',
    titulo: '',
    descripcion: '',
    fecha_evento: '',
    hora_inicio: '',
    hora_fin: '',
    ubicacion: '',
    estado: 'PROGRAMADO',
    observaciones: ''
  };

  // Propiedades para paginación
  currentPage: number = 1;
  itemsPerPage: number = 5;
  totalPages: number = 1;
  displayedEventos: Event[] = [];

  // Propiedades para filtros
  filtroTipo: string = '';
  filtroEstado: string = '';
  filtroAnio: string = '';

  constructor(
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadEventos();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['personalId'] && this.personalId) {
      this.nuevoEvento.id_personal = parseInt(this.personalId);
      this.loadEventos();
    }
  }

  // Cargar eventos del personal
  loadEventos(): void {
    if (!this.personalId) return;

    this.isLoading = true;
    // TODO: Implementar servicio de eventos
    // Por ahora simulamos datos
    setTimeout(() => {
      this.eventos = [
        {
          id_evento: 1,
          id_personal: parseInt(this.personalId!),
          tipo_evento: 'CAPACITACION',
          titulo: 'Capacitación en Seguridad Laboral',
          descripcion: 'Capacitación obligatoria sobre normas de seguridad en el trabajo',
          fecha_evento: '2024-01-15',
          hora_inicio: '09:00',
          hora_fin: '12:00',
          ubicacion: 'Sala de Conferencias A',
          estado: 'COMPLETADO',
          observaciones: 'Participación satisfactoria'
        },
        {
          id_evento: 2,
          id_personal: parseInt(this.personalId!),
          tipo_evento: 'EVALUACION',
          titulo: 'Evaluación de Desempeño Q1',
          descripcion: 'Evaluación trimestral de desempeño laboral',
          fecha_evento: '2024-03-30',
          hora_inicio: '14:00',
          hora_fin: '15:30',
          ubicacion: 'Oficina de RRHH',
          estado: 'COMPLETADO',
          observaciones: 'Resultado: Satisfactorio'
        }
      ];
      this.updateDisplayedEventos();
      this.isLoading = false;
      this.cdr.detectChanges();
    }, 1000);
  }

  // Guardar nuevo evento
  guardarEvento(): void {
    if (!this.validarEvento()) {
      return;
    }

    this.isSaving = true;
    // TODO: Implementar servicio para guardar evento
    setTimeout(() => {
      this.eventos.unshift({ ...this.nuevoEvento, id_evento: Date.now() });
      this.updateDisplayedEventos();
      this.resetForm();
      this.isSaving = false;
      this.notificationService.success('Evento creado exitosamente');
      this.cdr.detectChanges();
    }, 1000);
  }

  // Validar evento
  validarEvento(): boolean {
    if (!this.nuevoEvento.titulo.trim()) {
      this.notificationService.error('El título es obligatorio');
      return false;
    }
    if (!this.nuevoEvento.descripcion.trim()) {
      this.notificationService.error('La descripción es obligatoria');
      return false;
    }
    if (!this.nuevoEvento.fecha_evento) {
      this.notificationService.error('La fecha del evento es obligatoria');
      return false;
    }
    if (!this.nuevoEvento.hora_inicio) {
      this.notificationService.error('La hora de inicio es obligatoria');
      return false;
    }
    if (!this.nuevoEvento.hora_fin) {
      this.notificationService.error('La hora de fin es obligatoria');
      return false;
    }
    return true;
  }

  // Resetear formulario
  resetForm(): void {
    this.nuevoEvento = {
      id_personal: parseInt(this.personalId || '0'),
      tipo_evento: 'CAPACITACION',
      titulo: '',
      descripcion: '',
      fecha_evento: '',
      hora_inicio: '',
      hora_fin: '',
      ubicacion: '',
      estado: 'PROGRAMADO',
      observaciones: ''
    };
  }

  // Actualizar eventos mostrados con filtros y paginación
  updateDisplayedEventos(): void {
    let filteredEventos = [...this.eventos];

    // Aplicar filtros
    if (this.filtroTipo) {
      filteredEventos = filteredEventos.filter(e => e.tipo_evento === this.filtroTipo);
    }
    if (this.filtroEstado) {
      filteredEventos = filteredEventos.filter(e => e.estado === this.filtroEstado);
    }
    if (this.filtroAnio) {
      filteredEventos = filteredEventos.filter(e => e.fecha_evento.startsWith(this.filtroAnio));
    }

    // Calcular paginación
    this.totalPages = Math.ceil(filteredEventos.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.displayedEventos = filteredEventos.slice(startIndex, endIndex);
  }

  // Métodos de paginación
  onPageSizeChange(newSize: number): void {
    this.itemsPerPage = newSize;
    this.currentPage = 1;
    this.updateDisplayedEventos();
  }

  goToPage(page: number): void {
    this.currentPage = page;
    this.updateDisplayedEventos();
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // Aplicar filtros
  aplicarFiltros(): void {
    this.currentPage = 1;
    this.updateDisplayedEventos();
  }

  // Limpiar filtros
  limpiarFiltros(): void {
    this.filtroTipo = '';
    this.filtroEstado = '';
    this.filtroAnio = '';
    this.currentPage = 1;
    this.updateDisplayedEventos();
  }

  // Obtener clase CSS para el estado
  getEstadoClass(estado: string): string {
    switch (estado) {
      case 'PROGRAMADO': return 'bg-blue-100 text-blue-800';
      case 'EN_CURSO': return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETADO': return 'bg-green-100 text-green-800';
      case 'CANCELADO': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Obtener clase CSS para el tipo
  getTipoClass(tipo: string): string {
    switch (tipo) {
      case 'CAPACITACION': return 'bg-purple-100 text-purple-800';
      case 'REUNION': return 'bg-indigo-100 text-indigo-800';
      case 'EVALUACION': return 'bg-orange-100 text-orange-800';
      case 'DISCIPLINARIO': return 'bg-red-100 text-red-800';
      case 'RECONOCIMIENTO': return 'bg-green-100 text-green-800';
      case 'OTRO': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Formatear fecha
  formatearFecha(fecha: string): string {
    if (!fecha) return '';
    return new Date(fecha).toLocaleDateString('es-ES');
  }

  // Formatear hora
  formatearHora(hora: string): string {
    if (!hora) return '';
    return hora;
  }
}
