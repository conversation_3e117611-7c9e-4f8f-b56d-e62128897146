const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Habilitar CORS para Angular
app.use(cors({
  origin: 'http://localhost:4200',
  credentials: true
}));

// Crear directorio si no existe
const uploadDir = path.join(__dirname, 'public', 'uploads', 'contratos');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log('📁 Directorio creado:', uploadDir);
}

// Configurar multer para guardar archivos
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // El nombre personalizado se manejará después de recibir el archivo
    // Por ahora usar nombre temporal
    const tempName = Date.now() + '_' + file.originalname;
    cb(null, tempName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function (req, file, cb) {
    // Solo permitir PDFs
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Solo se permiten archivos PDF'), false);
    }
  }
});

// Endpoint para subir archivos
app.post('/api/upload/contrato', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No se recibió ningún archivo' });
    }

    // Generar nombre personalizado con DNI, fecha/hora y nombres
    const fechaHora = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const dni = req.body.empleado_dni || 'SIN_DNI';
    const nombres = req.body.empleado_nombres ? req.body.empleado_nombres.replace(/\s+/g, '_').toUpperCase() : 'SIN_NOMBRES';
    const nombrePersonalizado = `${dni}_${fechaHora}_${nombres}.pdf`;

    // Rutas de archivos
    const rutaTemporal = req.file.path;
    const rutaFinal = path.join(uploadDir, nombrePersonalizado);

    // Renombrar archivo con el nombre personalizado
    fs.renameSync(rutaTemporal, rutaFinal);

    const archivoGuardado = {
      nombreOriginal: req.file.originalname,
      nombreFinal: nombrePersonalizado,
      tamaño: req.file.size,
      ruta: rutaFinal,
      contrato_id: req.body.contrato_id,
      empleado_dni: req.body.empleado_dni,
      empleado_nombres: req.body.empleado_nombres
    };

    console.log('✅ Archivo guardado exitosamente:');
    console.log('📄 Nombre original:', archivoGuardado.nombreOriginal);
    console.log('📁 Nombre final:', archivoGuardado.nombreFinal);
    console.log('📊 Tamaño:', (archivoGuardado.tamaño / 1024).toFixed(2), 'KB');
    console.log('📂 Ruta completa:', archivoGuardado.ruta);

    res.json({
      success: true,
      message: 'Archivo subido exitosamente',
      archivo: archivoGuardado
    });

  } catch (error) {
    console.error('❌ Error al subir archivo:', error);
    res.status(500).json({
      error: 'Error interno del servidor',
      details: error.message
    });
  }
});

// Servir archivos estáticos desde la carpeta de uploads
app.use('/files', express.static(uploadDir));

// Endpoint para descargar archivos específicos
app.get('/api/download/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(uploadDir, filename);

    // Verificar que el archivo existe
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Archivo no encontrado' });
    }

    console.log(`📥 Descargando archivo: ${filename}`);

    // Configurar headers para descarga
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/pdf');

    // Enviar archivo
    res.sendFile(filePath);

  } catch (error) {
    console.error('❌ Error al descargar archivo:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Endpoint de salud
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Servidor de uploads funcionando' });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log('🚀 Servidor de uploads iniciado en puerto', PORT);
  console.log('📁 Directorio de uploads:', uploadDir);
  console.log('🌐 Endpoint disponible: http://localhost:3001/api/upload/contrato');
});
