import { Component, OnInit, ChangeDetectorRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { PersonalService, Personal } from '../../servicios/personal.service';
import { EmpresaService, Empresa } from '../../servicios/empresa.service';
import { PersonalContractsComponent } from './personal-contracts/personal-contracts.component';
import { PersonalVacationsComponent } from './personal-vacations/personal-vacations.component';
import { PersonalEventsComponent } from './personal-events/personal-events.component';
import { NotificationService } from '../../servicios/notification.service';
import { NumeroALetras } from '../../utils/numero-a-letras';

@Component({
  selector: 'app-personal-details',
  standalone: true,
  imports: [CommonModule, FormsModule, PersonalContractsComponent, PersonalVacationsComponent, PersonalEventsComponent],
  templateUrl: './personal-details.component.html',
  styleUrls: ['./personal-details.component.scss'],
  providers: [PersonalService, EmpresaService]
})
export class PersonalDetailsComponent implements OnInit {

  importe: number | null = 1130;
  enLetras: string = '';

  personalId: string | null = null;
  activeTab: string = 'perfil';
  isLoading: boolean = false;
  isSaving: boolean = false;
  isSearchingDni: boolean = false;
  empresas: Empresa[] = [];
  cargos: string[] = [
    'ASISTENTE DE GERENCIA',
    'ASISTENTE DE GERENCIA COMERCIAL',
    'ASISTENTE DE PROCESOS TECNOLOGICOS',
    'ASISTENTE DE RECURSOS HUMANOS',
    'ASISTENTE DE TECNOLOGIA DE LA INFORMACION',
    'ASISTENTE DE TESORERIA',
    'COORDINADOR DE PROYECTOS TECNOLOGICOS',
    'BACK OFFICE',
    'CAPACITADOR',
    'COACHING - PSICOLOGO',
    'CONSERJE',
    'DISEÑADOR',
    'FISOTERAPEUTA',
    'GERENTE COMERCIAL',
    'JEFE DE BACK OFFICE',
    'JEFE DE MANTENIMIENTO Y SERVICIOS AUXILIARES',
    'JEFE DE TECNOLOGIA',
    'META BUSINESS MANAGER',
    'OPERARIO LOGISTICO',
    'SUPERVISOR',
    'TELEOPERADOR'
  ];
  cdr = inject(ChangeDetectorRef);

  personal: Partial<Personal> = this.getEmptyPersonal();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private personalService: PersonalService,
    private empresaService: EmpresaService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {

    const m = Number(this.importe ?? 0);
    this.enLetras = NumeroALetras(m, {
      monedaPlural: 'SOLES',
      monedaSingular: 'SOL',
      mayus: true,               // => "MAYÚSCULAS"
      usarCon: 'CON',            // "CON" o "Y"
      normalizarUnMil: true      // "UN MIL" -> "MIL" (opcional)
    });

    console.log(this.enLetras);

    this.cargarEmpresas();
    this.route.paramMap.subscribe(params => {
      this.personalId = params.get('id');

      if (this.personalId === 'new') {
        this.personal = this.getEmptyPersonal();
      } else if (this.personalId) {
        this.isLoading = true;
        this.personalService.getPersonalById(Number(this.personalId)).subscribe({
          next: (data) => {
            this.personal = data; // Assign fetched data
            this.calcularTiempoServicio(this.personal.fecha_incorporacion);
            this.isLoading = false; // Set loading to false on success
            this.cdr.detectChanges();
          },
          error: (error) => {
            console.error('Error al cargar detalles del personal:', error);
            this.isLoading = false; // Set loading to false on error
            this.cdr.detectChanges();
          }
        });
      }
    });
  }

  private cargarEmpresas(): void {
    this.empresaService.getEmpresas().subscribe({
      next: (data) => {
        this.empresas = data;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Error al cargar empresas:', err);
      }
    });
  }

  buscarDNI(): void {
    if (!this.personal.documento || this.personal.documento.length !== 8) {
      this.notificationService.error('El DNI debe tener 8 dígitos.');
      return;
    }

    this.isSearchingDni = true;
    this.personalService.searchDni(this.personal.documento).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const data = response.data;
          this.personal.primer_nombre = data.nombres.split(' ')[0] || '';
          this.personal.segundo_nombre = data.nombres.split(' ').slice(1).join(' ') || '';
          this.personal.apellido_paterno = data.apellido_paterno;
          this.personal.apellido_materno = data.apellido_materno;
          this.personal.apellidos_nombres_completos = data.nombre_completo;
          this.personal.direccion = data.direccion;
          this.personal.departamento = data.departamento;
          this.personal.provincia = data.provincia;
          this.personal.distrito = data.distrito;
          this.personal.digito_verificador = data.codigo_verificacion;
          const sexo = data.sexo;
          console.log('Valor de sexo recibido:', sexo);
          console.log('Tipo de dato:', typeof sexo);
          console.log('Longitud:', sexo?.length);
          console.log('Código ASCII primer carácter:', sexo?.charCodeAt(0));

          // Limpiar el valor por si tiene espacios o caracteres extra
          const sexoLimpio = sexo?.toString().trim().toUpperCase();
          console.log('Sexo limpio:', sexoLimpio);

          if(sexoLimpio === 'M' || sexoLimpio === 'MASCULINO'){
            this.personal.genero = 'MASCULINO';
            console.log('Asignado: MASCULINO');
          } else if(sexoLimpio === 'F' || sexoLimpio === 'FEMENINO'){
            this.personal.genero = 'FEMENINO';
            console.log('Asignado: FEMENINO');
          } else {
            console.log('Valor de sexo no reconocido:', sexoLimpio);
            this.personal.genero = 'MASCULINO'; // Valor por defecto
          }

          // IMPORTANTE: No asignar el valor original de sexo después de la conversión
          // Comentar o eliminar cualquier línea que haga: this.personal.genero = sexo;

          // Debug final: verificar el estado del objeto personal
          console.log('Estado final del personal.genero:', this.personal.genero);
          console.log('Objeto personal completo:', this.personal);

          // Forzar detección de cambios para actualizar la vista
          this.cdr.detectChanges();

          // Verificar después de un pequeño delay si el valor se mantiene
          setTimeout(() => {
            console.log('Verificación después de 100ms - personal.genero:', this.personal.genero);
            const selectElement = document.getElementById('genero') as HTMLSelectElement;
            if (selectElement) {
              console.log('Valor del select en el DOM:', selectElement.value);
              // Forzar el valor en el DOM si es necesario
              if (selectElement.value !== this.personal.genero && this.personal.genero) {
                console.log('Forzando valor en el select...');
                selectElement.value = this.personal.genero;
                // Disparar evento change para que Angular detecte el cambio
                selectElement.dispatchEvent(new Event('change'));
              }
            }
          }, 100);
          
          const parts = data.fecha_nacimiento.split('/');
          if (parts.length === 3) {
            const birthDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
            this.personal.fecha_nacimiento = birthDate;
            this.personal.edad = this.calculateAge(birthDate);
          }

          // this.personal.genero = data.sexo; // ← COMENTADO: Ya se asigna arriba con la conversión M->MASCULINO
        } else {
          this.notificationService.info('No se encontraron datos para el DNI proporcionado.');
        }
        this.isSearchingDni = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Error en la búsqueda de DNI:', err);
        this.notificationService.error('Ocurrió un error al buscar el DNI.');
        this.isSearchingDni = false;
        this.cdr.detectChanges();
      }
    });
  }

  private calculateAge(birthDateString: string): number {
    if (!birthDateString) return 0;
    const birthDate = new Date(birthDateString);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
  }

  calcularTiempoServicio(fechaIncorporacion: string | null | undefined): void {
    if (!fechaIncorporacion) {
      this.personal.tiempo_servicio = '';
      return;
    }

    const fechaInicio = new Date(fechaIncorporacion);
    const hoy = new Date();

    // Ajustar las fechas a medianoche para evitar problemas con las zonas horarias
    fechaInicio.setUTCHours(0, 0, 0, 0);
    hoy.setUTCHours(0, 0, 0, 0);

    if (fechaInicio > hoy) {
      this.personal.tiempo_servicio = 'Fecha futura';
      return;
    }
    
    let years = hoy.getFullYear() - fechaInicio.getFullYear();
    let months = hoy.getMonth() - fechaInicio.getMonth();
    let days = hoy.getDate() - fechaInicio.getDate();

    if (days < 0) {
      months--;
      // Obtener el último día del mes anterior a 'hoy'
      const ultimoDiaMesAnterior = new Date(hoy.getFullYear(), hoy.getMonth(), 0).getDate();
      days += ultimoDiaMesAnterior;
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    this.personal.tiempo_servicio = `${years} años ${months} meses ${days} días`;
  }

  getEmptyPersonal(): Partial<Personal> {
    return {
      id_empresa: null,
      primer_nombre: '',
      segundo_nombre: '',
      apellido_paterno: '',
      apellido_materno: '',
      tipo_documento: 'DNI',
      documento: '',
      apellidos_nombres_completos: '',
      tipo_contrato: 'Indefinido',
      fecha_incorporacion: '',
      fecha_cese: null,
      fecha_nacimiento: '',
      edad: 0,
      direccion: '',
      departamento: '',
      provincia: '',
      distrito: '',
      telefono_movil: '',
      correo_electronico: '',
      cargo: '',
      sistema_pensiones: 'ONP',
      nombre_institucion_educativa: '',
      carrera_profesional: '',
      institucion_bancaria: '',
      cuenta_sueldo: '',
      horario: 'FULL TIME',
      salario: 0,
      condicion_trabajo_alimentacion: '',
      break_time: '',
      asignacion_familiar: 'NO',
      importe: 0,
      motivo_cese: '',
      importe_liquidacion: 0,
      genero: 'M',
      coordinador: '',
      tiempo_servicio: '',
      inicio_contrato: '',
      fin_contrato: '',
      fecha_vencimiento_periodo_prueba: '',
      sede: '',
      digito_verificador: '',
      respuesta_rit: 'NO',
      confidencialidad: '0',
      carga_familiar: '0',
      legalizacion_mapfre: '0',
      ruc: '',
      razon_social: ''
    };
  }

  selectTab(tab: string): void {
    this.activeTab = tab;
  }

  guardar(): void {
    this.isSaving = true;

    // Clonar el objeto para no modificar el modelo del formulario directamente
    const payload: Partial<Personal> = { ...this.personal };


    // Convertir fechas vacías a null antes de enviar
    const dateFields: (keyof Personal)[] = [
      'fecha_incorporacion',
      'fecha_cese',
      'fecha_nacimiento',
      'inicio_contrato',
      'fin_contrato',
      'fecha_vencimiento_periodo_prueba'
    ];

    dateFields.forEach(field => {
      if (!payload[field]) {
        delete payload[field];
      }
    });


    const handleSuccess = (message: string) => {
      this.isSaving = false;
      this.notificationService.success(message);
      setTimeout(() => {
        this.router.navigate(['/main-menu/personal']);
      }, 2000); // 2 seconds delay to allow notification to be seen
    };

    const handleError = (errorType: string, err: any) => {
      this.isSaving = false;
      console.error(`Error al ${errorType} personal:`, err);
      this.notificationService.error(`Ocurrió un error al ${errorType} el registro.`);
    };

    if (this.personalId === 'new') {
      this.personalService.createPersonal(payload).subscribe({
        next: () => handleSuccess('Personal creado correctamente.'),
        error: (err) => handleError('crear', err)
      });
    } else {
      this.personalService.updatePersonal(Number(this.personalId), payload).subscribe({
        next: () => handleSuccess('Personal actualizado correctamente.'),
        error: (err) => handleError('actualizar', err)
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/main-menu/personal']);
  }
}
