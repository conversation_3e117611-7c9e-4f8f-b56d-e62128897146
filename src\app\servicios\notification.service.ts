import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

export interface Notification {
  message: string;
  type: 'success' | 'error' | 'info';
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationSubject = new Subject<Notification>();
  notification$ = this.notificationSubject.asObservable();

  show(notification: Notification) {
    console.log('NotificationService: Enviando notificación:', notification);
    this.notificationSubject.next(notification);
  }

  success(message: string, duration: number = 5000) {
    this.show({ message, type: 'success', duration });
  }

  error(message: string, duration: number = 7000) {
    this.show({ message, type: 'error', duration });
  }

  info(message: string, duration: number = 5000) {
    this.show({ message, type: 'info', duration });
  }
}
