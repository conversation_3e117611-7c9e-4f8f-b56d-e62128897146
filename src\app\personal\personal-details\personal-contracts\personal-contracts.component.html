<div class="max-w-6xl mx-auto">
    <!-- Collapsible Card for Contract Creation -->
    <div class="bg-white p-0 mt-2 mx-2 rounded-lg shadow-md">
        <!-- Collapsible Card Header -->
        <div class="bg-indigo-600 text-white p-3 rounded-t-lg cursor-pointer flex justify-between items-center"
            (click)="toggleExpand()">
            <h2 class="text-xl font-semibold">Gestión de Contratos</h2>
            <i class="bx" [ngClass]="isExpanded ? 'bx-chevron-up' : 'bx-chevron-down'"></i>
        </div>

        <!-- Collapsible Card Content -->
        <div *ngIf="isExpanded" class="p-4 rounded-b-lg">

            <!-- Mensaje de Error Personalizado -->
            <div *ngIf="showError" class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-red-500 mr-2">❌</span>
                        <span class="font-medium">{{ errorMessage }}</span>
                    </div>
                    <button (click)="ocultarError()" class="text-red-500 hover:text-red-700 font-bold text-lg">
                        ×
                    </button>
                </div>
            </div>

            <!-- Mensaje de Éxito Personalizado -->
            <div *ngIf="showSuccess" class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span class="font-medium">{{ successMessage }}</span>
                    </div>
                    <button (click)="ocultarExito()" class="text-green-500 hover:text-green-700 font-bold text-lg">
                        ×
                    </button>
                </div>
            </div>

            <!-- Sección: Datos del Empleador -->
            <div class="bg-gray-50 p-4 rounded-lg shadow-sm mb-4">
                <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Datos del Empleador</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-3">
                    <div>
                        <label for="empleador_razon_social" class="block text-sm font-medium text-gray-700">Razón
                            Social</label>
                        <input type="text" id="empleador_razon_social" [(ngModel)]="contrato.empleador_razon_social"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="empleador_ruc" class="block text-sm font-medium text-gray-700">RUC</label>
                        <input type="text" id="empleador_ruc" [(ngModel)]="contrato.empleador_ruc"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="empleador_domicilio"
                            class="block text-sm font-medium text-gray-700">Domicilio</label>
                        <input type="text" id="empleador_domicilio" [(ngModel)]="contrato.empleador_domicilio"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                            <label for="sede" class="block text-sm font-medium text-gray-700">Sede</label>
                            <select id="sede" [(ngModel)]="contrato.empleador_sede"
                                class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                                <option value="CHICLAYO">CHICLAYO</option>
                                <option value="PIURA">PIURA</option>
                                <option value="SAN MIGUEL - LIMA">SAN MIGUEL - LIMA</option>
                                <option value="LOS OLIVOS - LIMA">LOS OLIVOS - LIMA</option>
                            </select>
                        </div>
                    <div class="hidden">
                        <label for="empleador_departamento"
                            class="block text-sm font-medium text-gray-700">Departamento</label>
                        <input type="text" id="empleador_departamento" [(ngModel)]="contrato.empleador_departamento"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden">
                        <label for="empleador_provincia"
                            class="block text-sm font-medium text-gray-700">Provincia</label>
                        <input type="text" id="empleador_provincia" [(ngModel)]="contrato.empleador_provincia"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden">
                        <label for="empleador_distrito" class="block text-sm font-medium text-gray-700">Distrito</label>
                        <input type="text" id="empleador_distrito" [(ngModel)]="contrato.empleador_distrito"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                </div>
            </div>

            <!-- Sección: Datos del Empleado -->
            <div class="bg-gray-50 p-4 rounded-lg shadow-sm mb-4">
                <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Datos del Empleado</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-3 mt-3">
                    <div>
                        <label for="empleado_dni" class="block text-sm font-medium text-gray-700">DNI</label>
                        <input type="text" id="empleado_dni" [(ngModel)]="contrato.empleado_dni"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="empleado_nombres" class="block text-sm font-medium text-gray-700">Nombres
                            Completos</label>
                        <input type="text" id="empleado_nombres" [(ngModel)]="contrato.empleado_nombres"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div> 
                    <div>
                        <label for="empleado_domicilio"
                            class="block text-sm font-medium text-gray-700">Domicilio</label>
                        <input type="text" id="empleado_domicilio" [(ngModel)]="contrato.empleado_domicilio"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="empleado_cargo" class="block text-sm font-medium text-gray-700">Cargo</label>
                        <input type="text" id="empleado_cargo" [(ngModel)]="contrato.empleado_cargo"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden">
                        <label for="genero" class="block text-sm font-medium text-gray-700">Género</label>
                        <select id="genero" [(ngModel)]="contrato.genero"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                            <option value="FEMENINO">FEMENINO</option>
                            <option value="MASCULINO">MASCULINO</option>
                        </select>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-3 mt-3">
                    
                    <div class="hidden">
                        <label for="empleado_departamento"
                            class="block text-sm font-medium text-gray-700">Departamento</label>
                        <input type="text" id="empleado_departamento" [(ngModel)]="contrato.empleado_departamento"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden"> 
                        <label for="empleado_provincia"
                            class="block text-sm font-medium text-gray-700">Provincia</label>
                        <input type="text" id="empleado_provincia" [(ngModel)]="contrato.empleado_provincia"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden">
                        <label for="empleado_distrito" class="block text-sm font-medium text-gray-700">Distrito</label>
                        <input type="text" id="empleado_distrito" [(ngModel)]="contrato.empleado_distrito"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div *ngIf="contrato.categoria_contrato === 'COMERCIAL'">
                        <label for="empleado_cantidad_ventas" class="block text-sm font-medium text-gray-700">Cantidad
                            de Ventas</label>
                        <input type="number" id="empleado_cantidad_ventas"
                            [(ngModel)]="contrato.empleado_cantidad_ventas"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                </div>
            </div>

            <!-- Sección: Fechas y Sueldo -->
            <div class="bg-gray-50 p-4 rounded-lg shadow-sm mb-4">
                <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Detalles del Contrato</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-3">
                    <div>
                        <label for="fecha_inicio" class="block text-sm font-medium text-gray-700">F. Inicio</label>
                        <input type="date" id="fecha_inicio" [(ngModel)]="contrato.fecha_inicio"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="fecha_fin" class="block text-sm font-medium text-gray-700">F. Fin</label>
                        <input type="date" id="fecha_fin" [(ngModel)]="contrato.fecha_fin"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="categoria_contrato" class="block text-sm font-medium text-gray-700">Categoría
                            Contrato</label>
                        <select id="categoria_contrato" [(ngModel)]="contrato.categoria_contrato"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                            <option value="">Seleccionar categoría</option>
                            <option value="COMERCIAL">COMERCIAL</option>
                            <option value="ADMINISTRATIVO">ADMINISTRATIVO</option>
                        </select>
                    </div>
                    <div>
                        <label for="sueldo" class="block text-sm font-medium text-gray-700">Sueldo S/</label>
                        <input type="number" id="sueldo" [(ngModel)]="contrato.sueldo"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden">
                        <label for="numero_letras" class="block text-sm font-medium text-gray-700">Sueldo en
                            Letras</label>
                        <input type="text" id="numero_letras" [(ngModel)]="contrato.numero_letras" readonly
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div>
                        <label for="importe" class="block text-sm font-medium text-gray-700">Condición de Trabajo
                            (Alimentación) S/</label>
                        <input type="number" id="importe" [(ngModel)]="contrato.importe"
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                    <div class="hidden">
                        <label for="importe_letras" class="block text-sm font-medium text-gray-700">Importe en
                            Letras</label>
                        <input type="text" id="importe_letras" [value]="importeEnLetras" readonly
                            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                    </div>
                </div>
            </div>

            <div class="flex justify-end mb-4">
                <button (click)="crearContrato()"
                    class="px-4 py-2 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-sm">Crear
                    Contrato</button>
            </div>
        </div>
    </div>

    <!-- Separate Card for Existing Contracts Table -->
    <div class="bg-white p-4 mt-4 mx-2 rounded-lg shadow-md">
        <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Contratos Existentes</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-blue-100 text-custom-blue uppercase text-sm leading-normal">
                    <tr>
                        <th class="py-3 px-6 text-left">Empleado</th>
                        <th class="py-3 px-6 text-left">Fecha Inicio</th>
                        <th class="py-3 px-6 text-left">Fecha Fin</th>
                        <th class="py-3 px-6 text-left">Sueldo</th>
                        <th class="py-3 px-6 text-left">Categoría</th>
                        <th class="py-3 px-6 text-center">Descargar</th>
                        <th class="py-3 px-6 text-center">Acciones</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr *ngIf="contratos.length === 0">
                        <td colspan="7" class="px-4 py-3 whitespace-nowrap text-center text-sm text-gray-500">Sin
                            contratos registrados.</td>
                    </tr>

                    <!-- Iteración de contratos existentes -->
                    <tr *ngFor="let contrato of displayedContratos; let i = index" class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {{ contrato.empleado_nombres }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {{ formatearFecha(contrato.fecha_inicio) }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {{ formatearFecha(contrato.fecha_fin) }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            S/ {{ contrato.sueldo | number:'1.2-2' }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  [ngClass]="{
                                    'bg-blue-100 text-blue-800': contrato.categoria_contrato === 'COMERCIAL',
                                    'bg-green-100 text-green-800': contrato.categoria_contrato === 'ADMINISTRATIVO'
                                  }">
                                {{ contrato.categoria_contrato }}
                            </span>
                        </td>

                        <!-- Columna Descargar PDF -->
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                            <button
                                *ngIf="contrato.ruta_archivo"
                                (click)="descargarArchivoPDF(contrato)"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                                title="Descargar archivo PDF del contrato">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Ver
                            </button>
                            <span
                                *ngIf="!contrato.ruta_archivo"
                                class="inline-flex items-center px-3 py-2 text-sm text-gray-400">
                                Sin archivo
                            </span>
                        </td>

                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <!-- Botón Descargar PDF -->
                                <button
                                    (click)="descargarContratoExistente(contrato)"
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                                    title="Descargar PDF del contrato">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    PDF
                                </button>

                                <!-- Botón Subir Archivo -->
                                <button
                                    (click)="abrirModalSubirArchivo(contrato)"
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
                                    title="Subir archivo PDF del contrato">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    Subir
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="flex justify-between items-center mt-4">
            <div class="flex items-center space-x-4">
                <label for="itemsPerPage" class="text-custom-blue font-bold font-poppins">Elementos por página:</label>
                <select id="itemsPerPage" [ngModel]="itemsPerPage" (ngModelChange)="onPageSizeChange($event)"
                class="p-2 border rounded-md text-gray-800 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition duration-300 hover:border-indigo-500 font-poppins">
                <option *ngFor="let option of [5, 10, 20]" [value]="option" class="text-gray-800 bg-white hover:bg-indigo-500 hover:text-white">{{ option }}</option>
              </select>
            </div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button (click)="goToPage(currentPage - 1)" [disabled]="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                  aria-hidden="true">
                  <path fill-rule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </button>
              <ng-container>
                <button *ngFor="let page of getPageNumbers()" (click)="goToPage(page)"
                  [class.bg-indigo-50]="currentPage === page" [class.border-indigo-500]="currentPage === page"
                  [class.text-indigo-600]="currentPage === page"
                  class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                  {{ page }}
                </button>
              </ng-container>
              <button (click)="goToPage(currentPage + 1)" [disabled]="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                  aria-hidden="true">
                  <path fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
    </div>

    <!-- Modal for Contract Preview -->
    <div *ngIf="showModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-3 bg-indigo-600 text-white p-3 rounded-t-md">
                <h3 class="text-lg font-semibold">Vista Previa del Contrato</h3>
                <button (click)="closeModal()" class="text-black">&times;</button>
            </div>
            <div class="modal-content overflow-auto" style="max-height: 70vh;" [innerHTML]="contratoGenerado"
                #contractPreview></div>
            <div class="flex justify-end mt-4">
                <button (click)="generarYGuardarContrato()"
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Crear Contrato</button>
                <button (click)="closeModal()"
                    class="ml-2 px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">Cerrar</button>
            </div>
        </div>
    </div>

    <!-- Modal para Subir Archivo PDF -->
    <div *ngIf="showModalSubirArchivo" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4 bg-green-600 text-white p-3 rounded-t-md">
                <h3 class="text-lg font-semibold">Subir Archivo PDF</h3>
                <button (click)="cerrarModalSubirArchivo()" class="text-white hover:text-gray-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="p-4">
                <!-- Información del contrato -->
                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                    <h4 class="font-medium text-gray-900 mb-2">Contrato seleccionado:</h4>
                    <p class="text-sm text-gray-600"><strong>Empleador:</strong> {{ contratoSeleccionado?.empleador_razon_social }}</p>
                    <p class="text-sm text-gray-600"><strong>Empleado:</strong> {{ contratoSeleccionado?.empleado_nombres }}</p>
                    <p class="text-sm text-gray-600"><strong>DNI:</strong> {{ contratoSeleccionado?.empleado_dni }}</p>
                    <p class="text-sm text-gray-600"><strong>Período:</strong> {{ contratoSeleccionado?.fecha_inicio | date:'dd/MM/yyyy' }} - {{ contratoSeleccionado?.fecha_fin | date:'dd/MM/yyyy' }}</p>
                </div>

                <!-- Área de subida de archivo -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Seleccionar archivo PDF <span class="text-red-500">*</span>
                    </label>

                    <!-- Input file oculto -->
                    <input
                        type="file"
                        #fileInput
                        (change)="onFileSelected($event)"
                        accept=".pdf"
                        class="hidden">

                    <!-- Área de drop/click -->
                    <div
                        (click)="fileInput.click()"
                        (dragover)="onDragOver($event)"
                        (dragleave)="onDragLeave($event)"
                        (drop)="onDrop($event)"
                        class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-green-400 transition-colors duration-200"
                        [class.border-green-400]="isDragOver"
                        [class.bg-green-50]="isDragOver">

                        <div *ngIf="!archivoSeleccionado">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p class="mt-2 text-sm text-gray-600">
                                <span class="font-medium text-green-600">Haz clic para seleccionar</span> o arrastra el archivo aquí
                            </p>
                            <p class="text-xs text-gray-500">Solo archivos PDF, máximo 10MB</p>
                        </div>

                        <div *ngIf="archivoSeleccionado" class="text-center">
                            <svg class="mx-auto h-12 w-12 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <p class="mt-2 text-sm font-medium text-gray-900">{{ archivoSeleccionado.name }}</p>
                            <p class="text-xs text-gray-500">{{ formatFileSize(archivoSeleccionado.size) }}</p>
                            <button
                                (click)="removerArchivo(); $event.stopPropagation()"
                                class="mt-2 text-xs text-red-600 hover:text-red-800">
                                Remover archivo
                            </button>
                        </div>
                    </div>

                    <!-- Mensaje de error -->
                    <div *ngIf="errorArchivo" class="mt-2 text-sm text-red-600">
                        {{ errorArchivo }}
                    </div>
                </div>

                <!-- Botones del modal -->
                <div class="flex justify-end space-x-3">
                    <button
                        (click)="cerrarModalSubirArchivo()"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Cancelar
                    </button>
                    <button
                        (click)="subirArchivo()"
                        [disabled]="!archivoSeleccionado || isSubiendoArchivo"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span *ngIf="!isSubiendoArchivo">Subir Archivo</span>
                        <span *ngIf="isSubiendoArchivo" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Subiendo...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>