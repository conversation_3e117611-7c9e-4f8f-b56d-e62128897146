<div class="space-y-6">
  <!-- Loading General -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div class="text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg text-gray-600">Cargando dashboard de personal...</span>
      </div>
      <p class="text-sm text-gray-500 mt-2">Por favor espere mientras se cargan los datos</p>
    </div>
  </div>

  <!-- Contenido principal (oculto durante loading) -->
  <div *ngIf="!isLoading && dashboardData">
    <!-- Header con selector de año -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold font-poppins text-custom-blue">Dashboard de Personal</h1>

      <!-- Selector de Año -->
      <div class="flex items-center bg-purple-100 border border-purple-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
        </svg>
        <span class="text-purple-800 text-sm font-medium mr-2">Año:</span>
        <select
          name="anio_seleccionado"
          [(ngModel)]="anioSeleccionado"
          (change)="onAnioChange()"
          class="bg-transparent text-purple-900 text-sm font-bold focus:outline-none cursor-pointer">
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024">2024</option>
          <option value="2025">2025</option>
          <option value="2026">2026</option>
          <option value="2027">2027</option>
        </select>
      </div>
    </div>

    <!-- Estadísticas generales -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
      <!-- Total Personal -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Personal</p>
            <p class="text-2xl font-bold text-gray-900">{{ dashboardData.total_personal }}</p>
          </div>
        </div>
      </div>

      <!-- Contratos Vigentes -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Contratos Vigentes</p>
            <p class="text-2xl font-bold text-green-600">{{ dashboardData.total_contratos_vigentes }}</p>
          </div>
        </div>
      </div>

      <!-- Contratos Vencidos -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Contratos Vencidos</p>
            <p class="text-2xl font-bold text-red-600">{{ dashboardData.total_contratos_vencidos }}</p>
          </div>
        </div>
      </div>

      <!-- Sin Contrato -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Sin Contrato</p>
            <p class="text-2xl font-bold text-gray-600">{{ dashboardData.total_sin_contrato }}</p>
          </div>
        </div>
      </div>

      <!-- Sin Archivo -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Sin Contrato Firmado</p>
            <p class="text-2xl font-bold text-orange-600">{{ dashboardData.total_sin_archivo }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Estadísticas por sede -->
    <div class="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
      <h2 class="text-lg font-semibold text-gray-800 mb-4">Personal por Sede</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div *ngFor="let sede of getSedesUnicas()" class="bg-gray-50 rounded-lg p-4 border border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">{{ sede }}</p>
              <p class="text-xl font-bold text-gray-900">{{ dashboardData.total_por_sede[sede] }}</p>
            </div>
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="flex items-center">
          <label class="text-sm font-medium text-gray-700 mr-2">Sede:</label>
          <select [(ngModel)]="filtroSede" (change)="updateDisplayedPersonal()"
                  class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Todas las sedes</option>
            <option *ngFor="let sede of getSedesUnicas()" [value]="sede">{{ sede }}</option>
          </select>
        </div>

        <div class="flex items-center">
          <label class="text-sm font-medium text-gray-700 mr-2">Estado Contrato:</label>
          <select [(ngModel)]="filtroEstadoContrato" (change)="updateDisplayedPersonal()"
                  class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Todos los estados</option>
            <option value="VIGENTE">Vigente</option>
            <option value="VENCIDO">Vencido</option>
            <option value="NO TIENE CONTRATO">No Tiene Contrato</option>
          </select>
        </div>

        <div class="flex items-center">
          <label class="text-sm font-medium text-gray-700 mr-2">Contrato Firmado:</label>
          <select [(ngModel)]="filtroContratoFirmado" (change)="updateDisplayedPersonal()"
                  class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Todos</option>
            <option value="CON_ARCHIVO">Subido</option>
            <option value="SIN_ARCHIVO">Faltante</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Tabla de Personal -->
    <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
          </svg>
          <h2 class="text-lg font-bold text-blue-800">Detalle del Personal</h2>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-blue-200">
          <thead class="bg-gradient-to-r from-blue-100 to-indigo-100">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Empleado</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">DNI</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Cargo</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Sede</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Fecha Inicio</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Fecha Fin</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Estado Contrato</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Contrato Firmado</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let persona of displayedPersonal; let i = index" 
                class="hover:bg-blue-50 transition-colors duration-150"
                [class.bg-blue-25]="i % 2 === 0">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-8 w-8">
                    <div class="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center">
                      <span class="text-xs font-medium text-white">
                        {{ persona.apellidos_nombres_completos.charAt(0) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ persona.apellidos_nombres_completos }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {{ persona.documento }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"/>
                  </svg>
                  {{ persona.cargo }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                  </svg>
                  {{ persona.sede }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                  </svg>
                  {{ formatearFecha(persona.fecha_inicio, persona) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                  </svg>
                  {{ formatearFecha(persona.fecha_fin, persona) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      [ngClass]="getEstadoContratoClass(persona)">
                  {{ getEstadoContrato(persona) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span [ngClass]="getContratoFirmadoClass(persona)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getContratoFirmadoTexto(persona) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Paginación -->
      <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-sm text-gray-700">Mostrar</span>
            <select [(ngModel)]="itemsPerPage" (change)="onPageSizeChange(itemsPerPage)"
                    class="mx-2 border border-gray-300 rounded px-2 py-1 text-sm">
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
            </select>
            <span class="text-sm text-gray-700">registros por página</span>
          </div>

          <div class="flex items-center space-x-2">
            <button (click)="goToPage(1)" [disabled]="currentPage === 1"
                    class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
              Primera
            </button>
            <button (click)="goToPage(currentPage - 1)" [disabled]="currentPage === 1"
                    class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
              Anterior
            </button>

            <span *ngFor="let page of getPageNumbers()"
                  class="px-3 py-1 text-sm border rounded cursor-pointer"
                  [ngClass]="page === currentPage ? 'bg-blue-500 text-white border-blue-500' : 'border-gray-300 hover:bg-gray-100'"
                  (click)="goToPage(page)">
              {{ page }}
            </span>

            <button (click)="goToPage(currentPage + 1)" [disabled]="currentPage === totalPages"
                    class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
              Siguiente
            </button>
            <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages"
                    class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
              Última
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error state -->
  <div *ngIf="!isLoading && !dashboardData" class="text-center py-12">
    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No se pudieron cargar los datos</h3>
    <p class="text-gray-500 mb-4">Hubo un problema al obtener la información del personal</p>
    <button (click)="loadDashboardData()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
      Reintentar
    </button>
  </div>
</div>
