<div class="bg-white p-8 rounded-lg shadow-lg">
  <div class="flex justify-between items-center mb-4">
    <h1 class="text-2xl font-bold font-poppins text-custom-blue">Gestión de Personal</h1>
    <button [routerLink]="['/main-menu/personal', 'new']" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
      Añadir Personal
    </button>
  </div>

  <div class="mb-4">
    <input type="text" placeholder="Buscar personal..." class="w-64 px-4 py-2 border rounded-md" [(ngModel)]="filterText"
      (ngModelChange)="onFilterTextChange(filterText)">
  </div>

<!-- Overlay tipo Sweet (versión más ancha) -->
<div *ngIf="isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
  <div class="flex flex-col items-center bg-white px-16 py-10 rounded-lg shadow-2xl w-[400px]">
    <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-500 border-solid mb-6"></div>
    <p class="text-gray-800 text-xl font-semibold text-center">Cargando personal, por favor espere...</p>
  </div>
</div>

  <div class="overflow-x-auto rounded-lg">
    <table class="min-w-full table-auto">

      <thead>
        <tr class="bg-blue-100 text-custom-blue uppercase text-sm leading-normal">
          <th class="py-3 px-6 text-left hidden">ID</th>
          <th class="py-3 px-6 text-left">T.D</th>
          <th class="py-3 px-6 text-left">DOCUMENTO</th>
          <th class="py-3 px-6 text-left">NOMBRES COMPLETOS</th>
          <th class="py-3 px-6 text-left">F. NACIMIENTO</th>
          <th class="py-3 px-6 text-left">EDAD</th>
          <th class="py-3 px-6 text-left">EMPRESA</th>
          <th class="py-3 px-6 text-left">CARGO</th>
          <th class="py-3 px-6 text-left">SEDE</th>
          <th class="py-3 px-6 text-center">Acciones</th>
        </tr>
      </thead>
      <tbody class="text-gray-800 text-sm font-light">
        <ng-container *ngIf="(displayedPersonal$ | async) as personalList">
          <tr *ngIf="personalList.length === 0">
            <td colspan="9" class="text-center py-4">
              Sin personal registrado
            </td>
          </tr>
          <tr *ngFor="let personal of personalList" class="border-b border-gray-200 hover:bg-blue-50">
            <td class="py-3 px-6 text-left whitespace-nowrap hidden">{{ personal.id_personal }}</td>
            <td class="py-3 px-6 text-left">{{ personal.tipo_documento }}</td>
            <td class="py-3 px-6 text-left">{{ personal.documento }}</td>
            <td class="py-3 px-6 text-left">{{ personal.apellidos_nombres_completos }}</td>
            <td class="py-3 px-6 text-left">{{ personal.fecha_nacimiento }}</td>
            <td class="py-3 px-6 text-left">{{ personal.edad }}</td>
            <td class="py-3 px-6 text-left">{{ personal.razon_social }}</td>
            <td class="py-3 px-6 text-left">{{ personal.cargo }}</td>
            <td class="py-3 px-6 text-left">{{ personal.sede }}</td>
            <td class="py-3 px-6 text-center">
              <button [routerLink]="['/main-menu/personal', personal.id_personal]"
                class="border border-red-500 text-red-500 hover:bg-red-500 hover:text-white text-xs py-1 px-2 rounded-full flex items-center transition-colors duration-300">
                <i class='bx bx-user-circle mr-1'></i>
                Ver perfil
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>

  <div class="flex justify-between items-center mt-4">
    <div class="flex items-center space-x-4">
      <label for="itemsPerPage" class="text-custom-blue font-bold font-poppins">Elementos por página:</label>
      <select id="itemsPerPage" [ngModel]="itemsPerPage" (ngModelChange)="onPageSizeChange($event)"
        class="p-2 border rounded-md text-gray-800 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition duration-300 hover:border-indigo-500 font-poppins">
        <option *ngFor="let option of pageSizeOptions" [value]="option" class="text-gray-800 bg-white hover:bg-indigo-500 hover:text-white">{{ option }}</option>
      </select>
    </div>
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
      <button (click)="goToPage(currentPage - 1)" [disabled]="currentPage === 1"
        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
        <span class="sr-only">Previous</span>
        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
          aria-hidden="true">
          <path fill-rule="evenodd"
            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
            clip-rule="evenodd" />
        </svg>
      </button>
      <ng-container *ngIf="totalPages$ | async as totalPages">
        <button *ngFor="let page of getPageNumbers(totalPages); let i = index" (click)="goToPage(i + 1)"
          [class.bg-indigo-50]="currentPage === i + 1" [class.border-indigo-500]="currentPage === i + 1"
          [class.text-indigo-600]="currentPage === i + 1"
          class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
          {{ i + 1 }}
        </button>
      </ng-container>
      <button (click)="goToPage(currentPage + 1)" [disabled]="currentPage === (totalPages$ | async)!"
        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
        <span class="sr-only">Next</span>
        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
          aria-hidden="true">
          <path fill-rule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clip-rule="evenodd" />
        </svg>
      </button>
    </nav>
  </div>
</div>