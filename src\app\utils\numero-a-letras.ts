// src/app/utils/numero-a-letras.ts
// Adaptación a TypeScript del código que compartiste, con opciones y tipado.
// Comentarios en español.

export interface NumeroALetrasOpciones {
  monedaPlural?: string;      // "SOLES"
  monedaSingular?: string;    // "SOL"
  mayus?: boolean;            // true => devuelve en MAYÚSCULAS
  usarCon?: 'CON' | 'Y';      // separador entre enteros y centavos ("CON" por defecto)
  normalizarUnMil?: boolean;  // true => "UN MIL" -> "MIL"
}

function Unidades(num: number): string {
  switch (num) {
    case 1: return "UN";
    case 2: return "DOS";
    case 3: return "TRES";
    case 4: return "CUATRO";
    case 5: return "CINCO";
    case 6: return "SEIS";
    case 7: return "SIETE";
    case 8: return "OCHO";
    case 9: return "NUEVE";
    default: return "";
  }
}

function Decenas(num: number): string {
  const decena = Math.floor(num / 10);
  const unidad = num - (decena * 10);

  switch (decena) {
    case 1:
      switch (unidad) {
        case 0: return "DIEZ";
        case 1: return "ONCE";
        case 2: return "DOCE";
        case 3: return "TRECE";
        case 4: return "CATORCE";
        case 5: return "QUINCE";
        default: return "DIECI" + Unidades(unidad);
      }
    case 2:
      switch (unidad) {
        case 0: return "VEINTE";
        default: return "VEINTI" + Unidades(unidad);
      }
    case 3: return DecenasY("TREINTA", unidad);
    case 4: return DecenasY("CUARENTA", unidad);
    case 5: return DecenasY("CINCUENTA", unidad);
    case 6: return DecenasY("SESENTA", unidad);
    case 7: return DecenasY("SETENTA", unidad);
    case 8: return DecenasY("OCHENTA", unidad);
    case 9: return DecenasY("NOVENTA", unidad);
    case 0: return Unidades(unidad);
    default: return "";
  }
}

function DecenasY(strSin: string, numUnidades: number): string {
  return numUnidades > 0 ? `${strSin} Y ${Unidades(numUnidades)}` : strSin;
}

function Centenas(num: number): string {
  const centenas = Math.floor(num / 100);
  const decenas = num - (centenas * 100);

  switch (centenas) {
    case 1: return decenas > 0 ? `CIENTO ${Decenas(decenas)}` : "CIEN";
    case 2: return `DOSCIENTOS ${Decenas(decenas)}`;
    case 3: return `TRESCIENTOS ${Decenas(decenas)}`;
    case 4: return `CUATROCIENTOS ${Decenas(decenas)}`;
    case 5: return `QUINIENTOS ${Decenas(decenas)}`;
    case 6: return `SEISCIENTOS ${Decenas(decenas)}`;
    case 7: return `SETECIENTOS ${Decenas(decenas)}`;
    case 8: return `OCHOCIENTOS ${Decenas(decenas)}`;
    case 9: return `NOVECIENTOS ${Decenas(decenas)}`;
    default: return Decenas(decenas);
  }
}

function Seccion(num: number, divisor: number, strSingular: string, strPlural: string): string {
  const cientos = Math.floor(num / divisor);
  const resto = num - (cientos * divisor);

  let letras = "";
  if (cientos > 0) {
    if (cientos > 1) letras = `${Centenas(cientos)} ${strPlural}`;
    else letras = strSingular;
  }
  if (resto > 0) letras += "";
  return letras;
}

function Miles(num: number): string {
  const divisor = 1000;
  const resto = num % divisor;

  const strMiles = Seccion(num, divisor, "UN MIL", "MIL");
  const strCentenas = Centenas(resto);

  if (strMiles === "") return strCentenas;
  return `${strMiles} ${strCentenas}`.trim();
}

function Millones(num: number): string {
  const divisor = 1000000;
  const resto = num % divisor;

  const strMillones = Seccion(num, divisor, "UN MILLON", "MILLONES");
  const strMiles = Miles(resto);

  if (strMillones === "") return strMiles;
  return `${strMillones} ${strMiles}`.trim();
}

function pad2(n: number): string {
  return String(n).padStart(2, '0');
}

/**
 * Convierte un número a letras (estilo comprobantes peruanos).
 * Ejemplos:
 *  - 1130   => "SON MIL CIENTO TREINTA SOLES CON 00/100"
 *  - 1.5    => "SON UN SOL CON 50/100"
 */
export function NumeroALetras(
  num: number,
  opciones: NumeroALetrasOpciones = {}
): string {
  const {
    monedaPlural = "SOLES",
    monedaSingular = "SOL",
    mayus = true,
    usarCon = "CON",
    normalizarUnMil = false,
  } = opciones;

  if (num === null || num === undefined || Number.isNaN(Number(num))) {
    return "";
  }

  // Manejo de negativos
  const negativo = Number(num) < 0;
  const abs = Math.abs(Number(num));

  const enteros = Math.floor(abs);
  const centavos = Math.round((abs - enteros) * 100);

  let letrasEntero = "";
  if (enteros === 0) {
    letrasEntero = "CERO";
  } else if (enteros === 1) {
    letrasEntero = Millones(enteros) + " " + monedaSingular;
  } else {
    letrasEntero = Millones(enteros) + " " + monedaPlural;
  }

  // Normalización opcional "UN MIL" -> "MIL"
  if (normalizarUnMil) {
    letrasEntero = letrasEntero.replace(/\bUN MIL\b/g, "MIL");
  }

  const sufijoCentavos = `${usarCon} ${pad2(centavos)}/100`;

  let resultado = `${letrasEntero}`;
  // Siempre incluimos centavos estilo "CON 00/100"
  // quitar esto para considerar los centavos
 // resultado = `${resultado} ${sufijoCentavos}`.trim();
  resultado = `${resultado}`;
  if (negativo) resultado = `MENOS ${resultado}`;

  return mayus ? resultado.toUpperCase() : resultado;
}
