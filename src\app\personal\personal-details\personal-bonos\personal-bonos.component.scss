// Estilos específicos para el componente de bonos
.bono-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.bono-status {
  &.pagado {
    @apply bg-green-100 text-green-800;
  }
  
  &.pendiente {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  &.cancelado {
    @apply bg-red-100 text-red-800;
  }
}

.forma-pago {
  &.transferencia {
    @apply bg-blue-100 text-blue-800;
  }
  
  &.efectivo {
    @apply bg-green-100 text-green-800;
  }
  
  &.cheque {
    @apply bg-purple-100 text-purple-800;
  }
}

// Estilos para el input de archivo
.file-input {
  &::-webkit-file-upload-button {
    @apply bg-indigo-50 text-indigo-700 border-0 rounded-md px-4 py-2 text-sm font-medium;
    
    &:hover {
      @apply bg-indigo-100;
    }
  }
}

// Animaciones para loading
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Estilos para montos
.monto-display {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #059669; // green-600
}

// Estilos para formularios
.form-section {
  @apply bg-gray-50 p-4 rounded-lg mb-4;
}

.required-field {
  @apply text-red-500;
}

// Responsive adjustments
@media (max-width: 768px) {
  .bono-table {
    font-size: 0.875rem;
  }
  
  .bono-filters {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .monto-display {
    font-size: 0.875rem;
  }
}

// Estilos para botones de acción
.action-button {
  @apply inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md;
  transition: all 0.2s ease-in-out;
  
  &.download {
    @apply text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50;
  }
  
  &:hover {
    transform: translateY(-1px);
  }
}

// Estilos para badges
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  
  &.success {
    @apply bg-green-100 text-green-800;
  }
  
  &.warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  &.danger {
    @apply bg-red-100 text-red-800;
  }
  
  &.info {
    @apply bg-blue-100 text-blue-800;
  }
  
  &.secondary {
    @apply bg-purple-100 text-purple-800;
  }
}
