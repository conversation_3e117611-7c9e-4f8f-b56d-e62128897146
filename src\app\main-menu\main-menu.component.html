<div class="flex h-screen bg-gray-100">
  <!-- Sidebar -->
  <div class="flex flex-col bg-white shadow-lg transition-all duration-300"
     [ngClass]="{ 'w-64': isMenuOpen, 'w-20 min-w-[5rem]': !isMenuOpen }">

    <div class="flex items-center justify-center h-20 shadow-md">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-indigo-500" *ngIf="isMenuOpen">MIDAS</h1>
        <p class="text-xs text-gray-500" *ngIf="isMenuOpen">SOFTWARE DE RECURSOS HUMANOS</p>
      </div>
    </div>
    <ul class="flex flex-col py-4 flex-grow">
      <li>
        <a routerLink="/main-menu/dashboard-personal" routerLinkActive="bg-gray-100 text-indigo-500" class="flex flex-row items-center h-12 transform hover:translate-x-2 transition-transform ease-in duration-200 text-gray-500 hover:text-indigo-500 hover:bg-gray-100">
          <span class="inline-flex items-center justify-center h-12 w-12 text-lg text-gray-400"><i class="bx bx-user"></i></span>
          <span class="text-sm font-medium" *ngIf="isMenuOpen">Dashboard Personal</span>
        </a>
      </li>
      <li>
        <a routerLink="/main-menu/dashboard" routerLinkActive="bg-gray-100 text-indigo-500" class="flex flex-row items-center h-12 transform hover:translate-x-2 transition-transform ease-in duration-200 text-gray-500 hover:text-indigo-500 hover:bg-gray-100">
          <span class="inline-flex items-center justify-center h-12 w-12 text-lg text-gray-400"><i class="bx bx-home"></i></span>
          <span class="text-sm font-medium" *ngIf="isMenuOpen">Dashboard Vacaciones</span>
        </a>
      </li>
      <!--<li>
        <a routerLink="/main-menu/empresas" routerLinkActive="bg-gray-100 text-indigo-500" class="flex flex-row items-center h-12 transform hover:translate-x-2 transition-transform ease-in duration-200 text-gray-500 hover:text-indigo-500 hover:bg-gray-100">
          <span class="inline-flex items-center justify-center h-12 w-12 text-lg text-gray-400"><i class="bx bx-building-house"></i></span>
          <span class="text-sm font-medium" *ngIf="isMenuOpen">Empresas</span>
        </a>
      </li>
      <li>
        <a routerLink="/main-menu/contratos" routerLinkActive="bg-gray-100 text-indigo-500" class="flex flex-row items-center h-12 transform hover:translate-x-2 transition-transform ease-in duration-200 text-gray-500 hover:text-indigo-500 hover:bg-gray-100">
          <span class="inline-flex items-center justify-center h-12 w-12 text-lg text-gray-400"><i class="bx bx-file"></i></span>
          <span class="text-sm font-medium" *ngIf="isMenuOpen">Contratos</span>
        </a>
      </li>-->
      <li>
        <a routerLink="/main-menu/personal" routerLinkActive="bg-gray-100 text-indigo-500" class="flex flex-row items-center h-12 transform hover:translate-x-2 transition-transform ease-in duration-200 text-gray-500 hover:text-indigo-500 hover:bg-gray-100">
          <span class="inline-flex items-center justify-center h-12 w-12 text-lg text-gray-400"><i class="bx bx-user"></i></span>
          <span class="text-sm font-medium" *ngIf="isMenuOpen">Personal</span>
        </a>
      </li>
      
      <li class="mt-auto">
        <a href="#" class="flex flex-row items-center h-12 transform hover:translate-x-2 transition-transform ease-in duration-200 text-white bg-red-500 hover:bg-red-600">
          <span class="inline-flex items-center justify-center h-12 w-12 text-lg text-white"><i class="bx bx-log-out"></i></span>
          <span class="text-sm font-medium" *ngIf="isMenuOpen">Salir</span>
        </a>
      </li>
    </ul>
  </div>

  <!-- Main content -->
  <div class="flex flex-col flex-grow">
    <!-- Header -->
    <div class="flex items-center justify-between h-20 px-6 bg-[#09215D] shadow-md">
      <button (click)="toggleMenu()" class="text-white hover:text-gray-200">
        <i class="bx bx-menu text-2xl"></i>
      </button>
      <div class="flex items-center">
        <div class="ml-4 flex items-center">
          <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="" class="w-10 h-10 rounded-full">
          <div class="ml-2">
            <p class="text-sm font-medium text-white">Enrique Piscoya</p>
            <p class="text-sm text-gray-300">Desarrollador</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="flex-grow p-6 overflow-y-auto">
      <router-outlet></router-outlet>
    </div>

    <!-- Footer -->
    <div class="py-4 text-center text-gray-500 text-sm">
      <p>© 2025, Diseñado por MIDAS. Codificado por Área de Tecnologías.</p>
    </div>
  </div>
</div>