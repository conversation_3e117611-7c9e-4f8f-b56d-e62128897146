.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 99999; // Aumentado para estar por encima de modales
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none; // Permite clicks a través del contenedor

  .notification {
    pointer-events: auto; // Pero permite clicks en las notificaciones
  }
}

.notification {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 400px;
  animation: fadeInOut 0.3s ease-in-out;

  &.notification-success {
    background-color: #28a745;
  }

  &.notification-error {
    background-color: #dc3545;
  }

  &.notification-info {
    background-color: #17a2b8;
  }

  .notification-icon {
    margin-right: 15px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;

    &.icon-success::before {
      content: '✓';
      font-weight: bold;
    }
    &.icon-error::before {
      content: '✕';
      font-weight: bold;
    }
    &.icon-info::before {
      content: 'ℹ';
      font-weight: bold;
    }
  }

  .notification-message {
    flex-grow: 1;
    font-size: 1rem;
  }

  .notification-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    opacity: 0.8;
    padding: 0 5px;

    &:hover {
      opacity: 1;
    }
  }
}
