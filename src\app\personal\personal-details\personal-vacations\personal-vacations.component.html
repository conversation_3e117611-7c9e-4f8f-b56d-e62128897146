<div class="space-y-6">
  <!-- Loading General -->
  <div *ngIf="isLoadingGeneral" class="flex justify-center items-center py-12">
    <div class="text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg text-gray-600">Cargando datos de vacaciones...</span>
      </div>
      <p class="text-sm text-gray-500 mt-2">Por favor espere mientras se cargan los datos</p>
    </div>
  </div>

  <!-- Contenido principal (oculto durante loading general) -->
  <div *ngIf="!isLoadingGeneral">
    <!-- Header -->
    <div class="flex justify-between items-center">
    <h2 class="text-lg font-bold font-poppins text-custom-blue mb-4">Gestión de Vacaciones</h2>
    <div class="flex flex-wrap items-center gap-3">
      <!-- Selector de Año -->
      <div class="flex items-center bg-purple-100 border border-purple-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
        </svg>
        <span class="text-purple-800 text-sm font-medium mr-2">Año:</span>
        <select name="anio_seleccionado"
          [(ngModel)]="anioSeleccionado"
          (change)="onAnioChange()"
          class="bg-transparent text-purple-900 text-sm font-bold focus:outline-none cursor-pointer">
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024">2024</option>
          <option value="2025">2025</option>
          <option value="2026">2026</option>
          <option value="2027">2027</option>
        </select>
      </div>

      <!-- Días Asignados -->
      <div class="flex items-center bg-indigo-100 border border-indigo-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-indigo-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
        </svg>
        <span class="text-indigo-800 text-sm font-medium">Asignados: </span>
        <span name= "dias_asignados" class="text-indigo-900 text-sm font-bold ml-1">{{ nuevaSolicitud.dias_asignados }}</span>
      </div>

      <!-- Días Disponibles -->
      <div class="hidden flex items-center bg-green-100 border border-green-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
        <span class="text-green-800 text-sm font-medium">Disponibles: </span>
        <span name= "dias_disponibles" class="text-green-900 text-sm font-bold ml-1">{{ nuevaSolicitud.dias_disponibles }}</span>
      </div>

      <!-- Días Utilizados -->
      <div class="flex items-center bg-red-100 border border-red-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        <span class="text-red-800 text-sm font-medium">Utilizados: </span>
        <span name="dias_utilizados" class="text-red-900 text-sm font-bold ml-1">{{ nuevaSolicitud.dias_utilizados }}</span>
      </div>

      <!-- Días Pendientes -->
      <div class="flex items-center bg-blue-100 border border-blue-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
        </svg>
        <span class="text-blue-800 text-sm font-medium">Pendientes: </span>
        <span name = "dias_pendientes" class="text-blue-900 text-sm font-bold ml-1">{{ nuevaSolicitud.dias_pendientes }}</span>
      </div>
    </div>
  </div>

  <!-- Nueva Solicitud de Vacaciones -->
  <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
    <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Nueva Solicitud de Vacaciones</h3>
    <div>
      <label for="tiempo_servicio" class="block text-sm font-medium text-gray-700">Tiempo de
          Servicio</label>
      <input type="text" id="tiempo_servicio" [(ngModel)]="personal.tiempo_servicio"
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
          readonly>
  </div> 
    <form (ngSubmit)="guardarSolicitud()" class="space-y-4">
      <!-- Tipo de Vacaciones -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-4">
          Tipo de Vacaciones <span class="text-red-500">*</span>
        </label>
        <div class="flex space-x-4">
          <!-- Radio Button Gozadas -->
          <div class="relative">
            <input
              type="radio"
              id="tipo_gozadas"
              [(ngModel)]="tipoVacaciones"
              name="tipo_vacaciones"
              value="GOZADAS"
              class="sr-only">
            <label
              for="tipo_gozadas"
              class="flex items-center px-6 py-3 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-md"
              [ngClass]="{
                'border-green-500 bg-green-50 text-green-700 shadow-sm': tipoVacaciones === 'GOZADAS',
                'border-gray-300 bg-white text-gray-700 hover:border-gray-400': tipoVacaciones !== 'GOZADAS'
              }">
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center"
                     [ngClass]="{
                       'border-green-500 bg-green-500': tipoVacaciones === 'GOZADAS',
                       'border-gray-300': tipoVacaciones !== 'GOZADAS'
                     }">
                  <div *ngIf="tipoVacaciones === 'GOZADAS'" class="w-2 h-2 rounded-full bg-white"></div>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                  </svg>
                  <span class="font-medium">Gozadas</span>
                </div>
              </div>
            </label>
          </div>

          <!-- Radio Button Compradas -->
          <div class="relative">
            <input
              type="radio"
              id="tipo_compradas"
              [(ngModel)]="tipoVacaciones"
              name="tipo_vacaciones"
              value="COMPRADAS"
              class="sr-only">
            <label
              for="tipo_compradas"
              class="flex items-center px-6 py-3 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-md"
              [ngClass]="{
                'border-blue-500 bg-blue-50 text-blue-700 shadow-sm': tipoVacaciones === 'COMPRADAS',
                'border-gray-300 bg-white text-gray-700 hover:border-gray-400': tipoVacaciones !== 'COMPRADAS'
              }">
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center"
                     [ngClass]="{
                       'border-blue-500 bg-blue-500': tipoVacaciones === 'COMPRADAS',
                       'border-gray-300': tipoVacaciones !== 'COMPRADAS'
                     }">
                  <div *ngIf="tipoVacaciones === 'COMPRADAS'" class="w-2 h-2 rounded-full bg-white"></div>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                    <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
                  </svg>
                  <span class="font-medium">Compradas</span>
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Sección para Vacaciones Gozadas -->
      <div *ngIf="tipoVacaciones === 'GOZADAS'" class="space-y-4">
        <!-- Fechas -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
        <div>
          <label for="fecha_inicio" class="block text-sm font-medium text-gray-700">
            Fecha de Inicio <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="fecha_inicio"
            [(ngModel)]="nuevaSolicitud.fecha_inicio"
            name="fecha_inicio"
            (change)="calcularDias()"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>

        <div>
          <label for="fecha_fin" class="block text-sm font-medium text-gray-700">
            Fecha de Fin <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="fecha_fin"
            [(ngModel)]="nuevaSolicitud.fecha_fin"
            name="fecha_fin"
            (change)="calcularDias()"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>

        <div>
          <label for="dias_solicitados" class="block text-sm font-medium text-gray-700">
            Días Solicitados
          </label>
          <input
            type="number"
            id="dias_solicitados"
            [(ngModel)]="nuevaSolicitud.dias_solicitados"
            name="dias_solicitados"
            readonly
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm bg-gray-50 text-gray-600 text-sm"
            [class.border-red-500]="nuevaSolicitud.dias_solicitados > nuevaSolicitud.dias_pendientes"
            [class.bg-red-50]="nuevaSolicitud.dias_solicitados > nuevaSolicitud.dias_pendientes">

          <!-- Advertencia visual cuando excede días pendientes -->
          <div *ngIf="nuevaSolicitud.dias_solicitados > nuevaSolicitud.dias_pendientes"
               class="mt-1 flex items-center text-red-600 text-xs">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <span>Excede los días pendientes ({{nuevaSolicitud.dias_pendientes}})</span>
          </div>
        </div>
      </div>

        <!-- Motivo -->
        <div>
          <label for="motivo" class="block text-sm font-medium text-gray-700">
            Motivo <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="motivo"
            [(ngModel)]="nuevaSolicitud.motivo"
            name="motivo"
            placeholder="Ej: Vacaciones familiares, descanso personal, etc."
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>
      </div>

      <!-- Sección para Vacaciones Compradas -->
      <div *ngIf="tipoVacaciones === 'COMPRADAS'" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div>
            <label for="dias_comprados" class="block text-sm font-medium text-gray-700">
              Días Comprados <span class="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="dias_comprados"
              [(ngModel)]="diasComprados"
              name="dias_comprados"
              (input)="calcularTotalCompra()"
              min="1"
              max="30"
              class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              [class.border-red-500]="diasComprados > nuevaSolicitud.dias_pendientes"
              [class.bg-red-50]="diasComprados > nuevaSolicitud.dias_pendientes"
              required>

            <!-- Advertencia visual cuando excede días pendientes -->
            <div *ngIf="diasComprados > nuevaSolicitud.dias_pendientes && diasComprados > 0"
                 class="mt-1 flex items-center text-red-600 text-xs">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
              <span>Excede los días pendientes ({{nuevaSolicitud.dias_pendientes}})</span>
            </div>
          </div>

          <div>
            <label for="precio_por_dia" class="block text-sm font-medium text-gray-700">
              Precio por Día <span class="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="precio_por_dia"
              [(ngModel)]="precioPorDia"
              name="precio_por_dia"
              (input)="calcularTotalCompra()"
              min="0"
              step="0.01"
              placeholder="0.00"
              class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              required>
          </div>

          <div>
            <label for="total_compra" class="block text-sm font-medium text-gray-700">
              Total
            </label>
            <input
              type="number"
              id="total_compra"
              [(ngModel)]="totalCompra"
              name="total_compra"
              readonly
              class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm bg-gray-50 text-gray-600 text-sm"
              placeholder="0.00">
          </div>
        </div>
      </div>

      <!-- Observaciones -->
      <div>
        <label for="observaciones" class="block text-sm font-medium text-gray-700">
          Observaciones (Opcional)
        </label>
        <textarea
          id="observaciones"
          [(ngModel)]="nuevaSolicitud.observaciones"
          name="observaciones"
          rows="3"
          placeholder="Información adicional sobre la solicitud..."
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm resize-none"></textarea>
      </div>

      <!-- Botones -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          (click)="resetForm()"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Limpiar
        </button>
        <button
          type="submit"
          [disabled]="isSaving"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="!isSaving">Enviar Solicitud</span>
          <span *ngIf="isSaving" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Enviando...
          </span>
        </button>
      </div>
    </form>
  </div>

  <!-- Historial de Vacaciones Gozadas -->
  <div class="bg-white rounded-lg shadow-md border border-gray-200 mb-6">
    <div class="p-6 border-b border-gray-200 bg-green-50">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-lg font-bold text-green-800">Historial de Vacaciones Gozadas</h3>
        </div>
        
        <!-- Filtros -->
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <select
            [(ngModel)]="filtroEstado"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los estados</option>
            <option value="PENDIENTE">Pendiente</option>
            <option value="APROBADO">Aprobado</option>
            <option value="RECHAZADO">Rechazado</option>
          </select>

          <select
            [(ngModel)]="filtroAnio"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los años</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>

          <button
            (click)="limpiarFiltros()"
            class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 focus:outline-none">
            Limpiar filtros
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div *ngIf="isLoading" class="p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-600">Cargando vacaciones...</span>
      </div>
    </div>

    <!-- Tabla de vacaciones gozadas -->
    <div *ngIf="!isLoading" class="overflow-x-auto">
      <table class="min-w-full divide-y divide-green-200">
        <thead class="bg-gradient-to-r from-green-100 to-emerald-100">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Período</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Días</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Estado</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Motivo</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Fecha Solicitud</th>
            
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngIf="displayedVacaciones.length === 0">
            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
              <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <p class="text-gray-500">No se encontraron registros de vacaciones</p>
              </div>
            </td>
          </tr>
          <tr *ngFor="let vacacion of displayedVacaciones; let i = index" 
              class="hover:bg-green-50 transition-colors duration-150"
              [class.bg-green-25]="i % 2 === 0">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8">
                  <div class="h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <div class="font-medium text-gray-900">{{ formatearFecha(vacacion.fecha_inicio) }}</div>
                  <div class="text-gray-500 text-xs">al {{ formatearFecha(vacacion.fecha_fin) }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                </svg>
                <span class="font-medium text-green-700">{{ vacacion.dias_solicitados }}</span>
                <span class="text-gray-500 ml-1">días</span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getEstadoClass(vacacion.estado)">
                {{ vacacion.estado }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-indigo-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <div class="max-w-xs truncate" [title]="vacacion.motivo">{{ vacacion.motivo }}</div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                {{ formatearFecha(vacacion.fecha_solicitud) }}
              </div>
            </td>
            
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div *ngIf="!isLoading && displayedVacaciones.length > 0" class="px-6 py-4 border-t border-gray-200">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700">Mostrar:</span>
          <select
            [(ngModel)]="itemsPerPage"
            (change)="onPageSizeChange(itemsPerPage)"
            class="border border-indigo-300 rounded px-2 py-1 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option [ngValue]="5">5</option>
            <option [ngValue]="10">10</option>
            <option [ngValue]="20">20</option>
          </select>
          <span class="text-sm text-gray-700">por página</span>
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            (click)="goToPage(currentPage - 1)"
            [disabled]="currentPage === 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Anterior
          </button>
          
          <button
            *ngFor="let page of getPageNumbers()"
            (click)="goToPage(page)"
            [class.bg-blue-600]="page === currentPage"
            [class.text-white]="page === currentPage"
            [class.text-gray-700]="page !== currentPage"
            [class.hover:bg-gray-50]="page !== currentPage"
            class="px-3 py-1 border border-gray-300 rounded text-sm">
            {{ page }}
          </button>
          
          <button
            (click)="goToPage(currentPage + 1)"
            [disabled]="currentPage === totalPages"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Siguiente
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Historial de Vacaciones Compradas -->
  <div class="bg-white rounded-lg shadow-md border border-gray-200">
    <div class="p-6 border-b border-gray-200 bg-blue-50">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-lg font-bold text-blue-800">Historial de Vacaciones Compradas</h3>
        </div>

        <!-- Filtros para vacaciones compradas -->
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <select
            [(ngModel)]="filtroEstadoCompradas"
            (change)="aplicarFiltrosCompradas()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los estados</option>
            <option value="PENDIENTE">Pendiente</option>
            <option value="APROBADO">Aprobado</option>
            <option value="RECHAZADO">Rechazado</option>
          </select>

          <select
            [(ngModel)]="filtroAnioCompradas"
            (change)="aplicarFiltrosCompradas()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los años</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>

          <button
            (click)="limpiarFiltrosCompradas()"
            class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 focus:outline-none">
            Limpiar filtros
          </button>
        </div>
      </div>
    </div>

    <!-- Loading para vacaciones compradas -->
    <div *ngIf="isLoadingCompradas" class="p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-600">Cargando vacaciones compradas...</span>
      </div>
    </div>

    <!-- Tabla de vacaciones compradas -->
    <div *ngIf="!isLoadingCompradas" class="overflow-x-auto">
      <table class="min-w-full divide-y divide-blue-200">
        <thead class="bg-gradient-to-r from-blue-100 to-cyan-100">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Días Comprados</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Precio por Día</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Total Pagado</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Estado</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Fecha Solicitud</th>
           
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngIf="displayedVacacionesCompradas.length === 0">
            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
              <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                </svg>
                <p class="text-gray-500">No se encontraron registros de vacaciones compradas</p>
              </div>
            </td>
          </tr>
          <tr *ngFor="let vacacion of displayedVacacionesCompradas; let i = index" 
              class="hover:bg-blue-50 transition-colors duration-150"
              [class.bg-blue-25]="i % 2 === 0">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8">
                  <div class="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                      <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <span class="font-medium text-blue-700">{{ vacacion.dias_comprados }}</span>
                  <span class="text-gray-500 ml-1">días</span>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                </svg>
                <span class="font-medium">S/ {{ vacacion.precio_por_dia | number:'1.2-2' }}</span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                </svg>
                <span class="font-bold text-green-600">S/ {{ vacacion.total_compra | number:'1.2-2' }}</span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getEstadoClass(vacacion.estado)">
                {{ vacacion.estado }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                {{ formatearFecha(vacacion.fecha_solicitud) }}
              </div>
            </td>
            
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación para vacaciones compradas -->
    <div *ngIf="!isLoadingCompradas && displayedVacacionesCompradas.length > 0" class="px-6 py-4 border-t border-gray-200">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700">Mostrar:</span>
          <select
            [(ngModel)]="itemsPerPageCompradas"
            (change)="onPageSizeChangeCompradas(itemsPerPageCompradas)"
            class="border border-indigo-300 rounded px-2 py-1 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option [ngValue]="5">5</option>
            <option [ngValue]="10">10</option>
            <option [ngValue]="20">20</option>
          </select>
          <span class="text-sm text-gray-700">por página</span>
        </div>

        <div class="flex items-center space-x-2">
          <button
            (click)="goToPageCompradas(currentPageCompradas - 1)"
            [disabled]="currentPageCompradas === 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Anterior
          </button>

          <button
            *ngFor="let page of getPageNumbersCompradas()"
            (click)="goToPageCompradas(page)"
            [class.bg-blue-600]="page === currentPageCompradas"
            [class.text-white]="page === currentPageCompradas"
            [class.text-gray-700]="page !== currentPageCompradas"
            [class.hover:bg-gray-50]="page !== currentPageCompradas"
            class="px-3 py-1 border border-gray-300 rounded text-sm">
            {{ page }}
          </button>

          <button
            (click)="goToPageCompradas(currentPageCompradas + 1)"
            [disabled]="currentPageCompradas === totalPagesCompradas"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Siguiente
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Fin del contenido principal -->
  </div>
</div>
