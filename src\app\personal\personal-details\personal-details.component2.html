<div class="bg-white p-4 mt-2 mx-2 rounded-lg shadow-md">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-4">
            <h1 class="text-2xl font-bold text-gray-800">Detalle de Personal</h1>
            <button (click)="goBack()"
                class="flex items-center px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors duration-200">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Volver  
            </button>
        </div>

        <!-- Overlay de carga -->
        @if (isLoading) {
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="flex flex-col items-center bg-white px-16 py-10 rounded-lg shadow-2xl w-[400px]">
                <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-500 border-solid mb-6"></div>
                <p class="text-gray-800 text-xl font-semibold text-center">Cargando datos, por favor espere...</p>
            </div>
        </div>
        }

        <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button (click)="selectTab('perfil')" [class.border-indigo-500]="activeTab === 'perfil'"
                    [class.text-indigo-600]="activeTab === 'perfil'" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
          hover:text-indigo-600 hover:border-indigo-300 transition-colors duration-200 ease-in-out"
                    [class.border-transparent]="activeTab !== 'perfil'" [class.text-gray-500]="activeTab !== 'perfil'">
                    PERFIL
                </button>
                <button (click)="selectTab('contratos')" [class.border-indigo-500]="activeTab === 'contratos'"
                    [class.text-indigo-600]="activeTab === 'contratos'" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
          hover:text-indigo-600 hover:border-indigo-300 transition-colors duration-200 ease-in-out"
                    [class.border-transparent]="activeTab !== 'contratos'"
                    [class.text-gray-500]="activeTab !== 'contratos'">
                    CONTRATOS
                </button>
                <button (click)="selectTab('boletas')" [class.border-indigo-500]="activeTab === 'boletas'"
                    [class.text-indigo-600]="activeTab === 'boletas'" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
          hover:text-indigo-600 hover:border-indigo-300 transition-colors duration-200 ease-in-out"
                    [class.border-transparent]="activeTab !== 'boletas'"
                    [class.text-gray-500]="activeTab !== 'boletas'">
                    BOLETAS DE PAGO
                </button>
                <button (click)="selectTab('vacaciones')" [class.border-indigo-500]="activeTab === 'vacaciones'"
                    [class.text-indigo-600]="activeTab === 'vacaciones'" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
          hover:text-indigo-600 hover:border-indigo-300 transition-colors duration-200 ease-in-out"
                    [class.border-transparent]="activeTab !== 'vacaciones'"
                    [class.text-gray-500]="activeTab !== 'vacaciones'">
                    VACACIONES
                </button>
            </nav>
        </div>  

        <div>
            <div *ngIf="activeTab === 'perfil'">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Información de Perfil</h2>

                <!-- Sección: Datos Personales -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Datos Personales</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="tipo_documento" class="block text-sm font-medium text-gray-700">Tipo
                                Documento</label>
                            <select id="tipo_documento" [(ngModel)]="personal.tipo_documento"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option value="DNI">DNI</option>
                                <option value="CE">Carnet de Extranjería</option>
                                <option value="PAS">Pasaporte</option>
                            </select>
                        </div>
                        <div>
                            <label for="documento" class="block text-sm font-medium text-gray-700">Documento</label>
                            <input type="text" id="documento" [(ngModel)]="personal.documento"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="primer_nombre" class="block text-sm font-medium text-gray-700">Primer
                                Nombre</label>
                            <input type="text" id="primer_nombre" [(ngModel)]="personal.primer_nombre"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="segundo_nombre" class="block text-sm font-medium text-gray-700">Segundo
                                Nombre</label>
                            <input type="text" id="segundo_nombre" [(ngModel)]="personal.segundo_nombre"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="apellido_paterno" class="block text-sm font-medium text-gray-700">Apellido
                                Paterno</label>
                            <input type="text" id="apellido_paterno" [(ngModel)]="personal.apellido_paterno"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="apellido_materno" class="block text-sm font-medium text-gray-700">Apellido
                                Materno</label>
                            <input type="text" id="apellido_materno" [(ngModel)]="personal.apellido_materno"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="apellidos_nombres_completos"
                                class="block text-sm font-medium text-gray-700">Apellidos y
                                Nombres Completos</label>
                            <input type="text" id="apellidos_nombres_completos"
                                [(ngModel)]="personal.apellidos_nombres_completos"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
                                readonly>
                        </div>
                        <div>
                            <label for="fecha_nacimiento" class="block text-sm font-medium text-gray-700">Fecha
                                Nacimiento</label>
                            <input type="date" id="fecha_nacimiento" [(ngModel)]="personal.fecha_nacimiento"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="edad" class="block text-sm font-medium text-gray-700">Edad</label>
                            <input type="number" id="edad" [(ngModel)]="personal.edad"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
                                readonly>
                        </div>
                        <div>
                            <label for="genero" class="block text-sm font-medium text-gray-700">Género</label>
                            <select id="genero" [(ngModel)]="personal.genero"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option value="M">Masculino</option>
                                <option value="F">Femenino</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Sección: Información de Contacto -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Información de Contacto</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="direccion" class="block text-sm font-medium text-gray-700">Dirección</label>
                            <input type="text" id="direccion" [(ngModel)]="personal.direccion"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="telefono_movil" class="block text-sm font-medium text-gray-700">Teléfono
                                Móvil</label>
                            <input type="text" id="telefono_movil" [(ngModel)]="personal.telefono_movil"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="correo_electronico" class="block text-sm font-medium text-gray-700">Correo
                                Electrónico</label>
                            <input type="email" id="correo_electronico" [(ngModel)]="personal.correo_electronico"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                    </div>
                </div>

                <!-- Sección: Información Laboral -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Información Laboral</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="id_empresa" class="block text-sm font-medium text-gray-700">Empresa</label>
                            <select id="id_empresa" [(ngModel)]="personal.id_empresa"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option [ngValue]="1">Empresa A</option>
                                <option [ngValue]="2">Empresa B</option>
                                <option [ngValue]="7">Empresa C (Temporal)</option>
                            </select>
                        </div>
                        <div>
                            <label for="cargo" class="block text-sm font-medium text-gray-700">Cargo</label>
                            <input type="text" id="cargo" [(ngModel)]="personal.cargo"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="tipo_contrato" class="block text-sm font-medium text-gray-700">Tipo de
                                Contrato</label>
                            <select id="tipo_contrato" [(ngModel)]="personal.tipo_contrato"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option value="Indefinido">Indefinido</option>
                                <option value="Temporal">Temporal</option>
                            </select>
                        </div>
                        <div>
                            <label for="fecha_incorporacion" class="block text-sm font-medium text-gray-700">Fecha de
                                Incorporación</label>
                            <input type="date" id="fecha_incorporacion" [(ngModel)]="personal.fecha_incorporacion"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="fecha_cese" class="block text-sm font-medium text-gray-700">Fecha de
                                Cese</label>
                            <input type="date" id="fecha_cese" [(ngModel)]="personal.fecha_cese"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="motivo_cese" class="block text-sm font-medium text-gray-700">Motivo de
                                Cese</label>
                            <input type="text" id="motivo_cese" [(ngModel)]="personal.motivo_cese"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="tiempo_servicio" class="block text-sm font-medium text-gray-700">Tiempo de
                                Servicio</label>
                            <input type="text" id="tiempo_servicio" [(ngModel)]="personal.tiempo_servicio"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
                                readonly>
                        </div>
                        <div>
                            <label for="inicio_contrato" class="block text-sm font-medium text-gray-700">Inicio
                                Contrato</label>
                            <input type="date" id="inicio_contrato" [(ngModel)]="personal.inicio_contrato"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="fin_contrato" class="block text-sm font-medium text-gray-700">Fin
                                Contrato</label>
                            <input type="date" id="fin_contrato" [(ngModel)]="personal.fin_contrato"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="fecha_vencimiento_periodo_prueba"
                                class="block text-sm font-medium text-gray-700">Fecha
                                Vencimiento Periodo Prueba</label>
                            <input type="date" id="fecha_vencimiento_periodo_prueba"
                                [(ngModel)]="personal.fecha_vencimiento_periodo_prueba"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="coordinador" class="block text-sm font-medium text-gray-700">Coordinador</label>
                            <input type="text" id="coordinador" [(ngModel)]="personal.coordinador"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                    </div>
                </div>

                <!-- Sección: Información Salarial y Beneficios -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Información Salarial y Beneficios</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="salario" class="block text-sm font-medium text-gray-700">Salario</label>
                            <input type="number" id="salario" [(ngModel)]="personal.salario"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="condicion_trabajo_alimentacion"
                                class="block text-sm font-medium text-gray-700">Condición
                                Trabajo Alimentación</label>
                            <input type="text" id="condicion_trabajo_alimentacion"
                                [(ngModel)]="personal.condicion_trabajo_alimentacion"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="break_time" class="block text-sm font-medium text-gray-700">Break Time</label>
                            <input type="text" id="break_time" [(ngModel)]="personal.break_time"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="asignacion_familiar" class="block text-sm font-medium text-gray-700">Asignación
                                Familiar</label>
                            <input type="text" id="asignacion_familiar" [(ngModel)]="personal.asignacion_familiar"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="importe" class="block text-sm font-medium text-gray-700">Importe</label>
                            <input type="number" id="importe" [(ngModel)]="personal.importe"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="importe_liquidacion" class="block text-sm font-medium text-gray-700">Importe
                                Liquidación</label>
                            <input type="number" id="importe_liquidacion" [(ngModel)]="personal.importe_liquidacion"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                    </div>
                </div>

                <!-- Sección: Información Bancaria y de Pensiones -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Información Bancaria y de Pensiones</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="sistema_pensiones" class="block text-sm font-medium text-gray-700">Sistema de
                                Pensiones</label>
                            <select id="sistema_pensiones" [(ngModel)]="personal.sistema_pensiones"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option value="ONP">ONP</option>
                                <option value="AFP">AFP</option>
                            </select>
                        </div>
                        <div>
                            <label for="institucion_bancaria"
                                class="block text-sm font-medium text-gray-700">Institución
                                Bancaria</label>
                            <input type="text" id="institucion_bancaria" [(ngModel)]="personal.institucion_bancaria"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="cuenta_sueldo" class="block text-sm font-medium text-gray-700">Cuenta
                                Sueldo</label>
                            <input type="text" id="cuenta_sueldo" [(ngModel)]="personal.cuenta_sueldo"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                    </div>
                </div>

                <!-- Sección: Información Académica -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Información Académica</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="nombre_institucion_educativa"
                                class="block text-sm font-medium text-gray-700">Nombre
                                Institución Educativa</label>
                            <input type="text" id="nombre_institucion_educativa"
                                [(ngModel)]="personal.nombre_institucion_educativa"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="carrera_profesional" class="block text-sm font-medium text-gray-700">Carrera
                                Profesional</label>
                            <input type="text" id="carrera_profesional" [(ngModel)]="personal.carrera_profesional"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                    </div>
                </div>

                <!-- Sección: Otros Datos -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Otros Datos</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                        <div>
                            <label for="digito_verificador" class="block text-sm font-medium text-gray-700">Dígito
                                Verificador</label>
                            <input type="text" id="digito_verificador" [(ngModel)]="personal.digito_verificador"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="respuesta_rit" class="block text-sm font-medium text-gray-700">Respuesta
                                RIT</label>
                            <input type="text" id="respuesta_rit" [(ngModel)]="personal.respuesta_rit"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                        <div>
                            <label for="confidencialidad"
                                class="block text-sm font-medium text-gray-700">Confidencialidad</label>
                            <select id="confidencialidad" [(ngModel)]="personal.confidencialidad"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option [ngValue]="1">Sí</option>
                                <option [ngValue]="0">No</option>
                            </select>
                        </div>
                        <div>
                            <label for="carga_familiar" class="block text-sm font-medium text-gray-700">Carga
                                Familiar</label>
                            <select id="carga_familiar" [(ngModel)]="personal.carga_familiar"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option [ngValue]="1">Sí</option>
                                <option [ngValue]="0">No</option>
                            </select>
                        </div>
                        <div>
                            <label for="legalizacion_mapfre"
                                class="block text-sm font-medium text-gray-700">Legalización
                                Mapfre</label>
                            <select id="legalizacion_mapfre" [(ngModel)]="personal.legalizacion_mapfre"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                                <option [ngValue]="1">Sí</option>
                                <option [ngValue]="0">No</option>
                            </select>
                        </div>
                        <div>
                            <label for="horario" class="block text-sm font-medium text-gray-700">Horario</label>
                            <input type="text" id="horario" [(ngModel)]="personal.horario"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base">
                        </div>
                    </div>
                </div>

                <!-- Sección: Información de Empresa (Relacionada) -->
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Información de Empresa (Relacionada)</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="ruc" class="block text-sm font-medium text-gray-700">RUC</label>
                            <input type="text" id="ruc" [(ngModel)]="personal.ruc"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
                                readonly>
                        </div>
                        <div>
                            <label for="razon_social" class="block text-sm font-medium text-gray-700">Razón
                                Social</label>
                            <input type="text" id="razon_social" [(ngModel)]="personal.razon_social"
                                class="mt-1 block w-full px-3 py-2 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
                                readonly>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                        class="px-6 py-2 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">Guardar</button>
                </div>
            </div>
            <div *ngIf="activeTab === 'contratos'">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Contratos</h2>
                <!-- Content for Contratos tab -->
                <p>Aquí se listarán los contratos del personal.</p>
            </div>
            <div *ngIf="activeTab === 'boletas'">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Boletas de Pago</h2>
                <!-- Content for Boletas de Pago tab -->
                <p>Aquí se mostrarán las boletas de pago del personal.</p>
            </div>
            <div *ngIf="activeTab === 'vacaciones'">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Vacaciones</h2>
                <!-- Content for Vacaciones tab -->
                <p>Aquí se gestionarán las vacaciones del personal.</p>
            </div>
        </div>
    </div>
</div>