import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-main-menu',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './main-menu.component.html',
  styleUrls: ['./main-menu.component.scss']
})
export class MainMenuComponent implements OnInit, OnDestroy {
  isMenuOpen = true;

  constructor() {
    console.log('MainMenuComponent initialized. isMenuOpen:', this.isMenuOpen);
  }

  ngOnInit(): void {
    console.log('MainMenuComponent ngOnInit. isMenuOpen:', this.isMenuOpen);
  }

  ngOnDestroy(): void {
    console.log('MainMenuComponent destroyed.');
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
    console.log('toggleMenu called. isMenuOpen:', this.isMenuOpen);
  }
}
