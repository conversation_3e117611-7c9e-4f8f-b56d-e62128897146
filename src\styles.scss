@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos globales para SweetAlert2 - Forzar visibilidad de botones */
.swal2-container {
  .swal2-popup {
    .swal2-actions {
      display: flex !important;
      justify-content: center !important;
      gap: 12px !important;
      margin-top: 20px !important;

      .swal2-styled {
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 !important;
        padding: 12px 24px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        border-radius: 8px !important;
        border: none !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        min-width: 130px !important;
        height: 44px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;

        &:hover, &:focus {
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        }
      }

      .swal2-confirm {
        background-color: #3085d6 !important;
        color: white !important;

        &:hover, &:focus {
          background-color: #2574c7 !important;
        }
      }

      .swal2-cancel {
        background-color: #6b7280 !important;
        color: white !important;

        &:hover, &:focus {
          background-color: #5b6470 !important;
        }
      }
    }
  }
}

/* Forzar visibilidad inmediata */
.swal2-actions .swal2-styled,
.swal2-confirm,
.swal2-cancel {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
}

/* Estilos para loading personalizado */
.swal2-loading-popup {
  .swal2-actions {
    display: none !important;
  }
}

/* Estilos para loading sin bordes */
.swal2-borderless {
  .swal2-actions,
  .swal2-footer {
    display: none !important;
  }

  .swal2-header {
    padding: 0 !important;
  }

  .swal2-content {
    padding: 0 !important;
    margin: 0 !important;
  }
}

/* Estilos para toasts sin botones */
.swal2-toast-no-buttons {
  .swal2-actions,
  .swal2-footer {
    display: none !important;
  }

  .swal2-header {
    padding: 0 !important;
  }

  .swal2-content {
    padding: 0 !important;
    margin: 0 !important;
  }
}

/* Estilos para SweetAlert CON botones (forzar visibilidad) */
.swal2-with-buttons {
  .swal2-actions {
    display: flex !important;
    justify-content: center !important;
    gap: 12px !important;
    margin-top: 20px !important;
  }

  .swal2-actions-visible {
    display: flex !important;
  }

  .swal2-confirm-visible,
  .swal2-cancel-visible {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-width: 130px !important;
    height: 44px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .swal2-confirm-visible {
    background-color: #3085d6 !important;
    color: white !important;

    &:hover, &:focus {
      background-color: #2574c7 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }
  }

  .swal2-cancel-visible {
    background-color: #6b7280 !important;
    color: white !important;

    &:hover, &:focus {
      background-color: #5b6470 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }
  }
}

/* Loader personalizado */
.swal2-loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ocultar completamente el botón deny (NO) - SOLO para clases específicas */
.swal2-borderless .swal2-deny,
.swal2-toast-no-buttons .swal2-deny {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Asegurar que solo aparezcan confirm y cancel - SOLO para clases específicas */
.swal2-borderless .swal2-actions .swal2-deny,
.swal2-toast-no-buttons .swal2-actions .swal2-deny {
  display: none !important;
}

/* Asegurar que los SweetAlert normales (sin clases específicas) muestren botones */
.swal2-popup:not(.swal2-borderless):not(.swal2-toast-no-buttons) {
  .swal2-actions {
    display: flex !important;
    justify-content: center !important;
    gap: 10px !important;
  }

  .swal2-confirm,
  .swal2-cancel {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-flex !important;
  }
}
