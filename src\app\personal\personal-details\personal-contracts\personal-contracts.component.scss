/* Estilos para el componente de contratos */

/* Estilos globales para SweetAlert2 - Forzar visibilidad de botones */
::ng-deep .swal2-container {
  .swal2-popup {
    .swal2-actions {
      display: flex !important;
      justify-content: center !important;
      gap: 10px !important;
      margin-top: 20px !important;

      .swal2-styled {
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 !important;
        padding: 12px 24px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        border-radius: 8px !important;
        border: none !important;
        cursor: pointer !important;
        transition: all 0.15s ease !important;
        min-width: 120px !important;
        height: 44px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
      }

      .swal2-confirm {
        background-color: #3085d6 !important;
        color: white !important;

        &:hover, &:focus {
          background-color: #2574c7 !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        }
      }

      .swal2-cancel {
        background-color: #6b7280 !important;
        color: white !important;

        &:hover, &:focus {
          background-color: #5b6470 !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        }
      }
    }
  }
}

/* Estilos adicionales para asegurar visibilidad */
.swal2-actions .swal2-styled {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
}

/* Estilos para clases personalizadas */
::ng-deep .swal2-popup-custom {
  .swal2-actions-custom {
    display: flex !important;
    justify-content: center !important;
    gap: 15px !important;
    margin-top: 25px !important;

    .swal2-confirm-custom,
    .swal2-cancel-custom {
      opacity: 1 !important;
      visibility: visible !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      margin: 0 !important;
      padding: 14px 28px !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      border-radius: 8px !important;
      border: none !important;
      cursor: pointer !important;
      transition: all 0.2s ease !important;
      min-width: 140px !important;
      height: 48px !important;
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12) !important;
      text-transform: none !important;
      letter-spacing: 0.5px !important;
    }

    .swal2-confirm-custom {
      background: linear-gradient(135deg, #3085d6 0%, #2574c7 100%) !important;
      color: white !important;

      &:hover, &:focus, &:active {
        background: linear-gradient(135deg, #2574c7 0%, #1e5fa8 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.18) !important;
      }
    }

    .swal2-cancel-custom {
      background: linear-gradient(135deg, #6b7280 0%, #5b6470 100%) !important;
      color: white !important;

      &:hover, &:focus, &:active {
        background: linear-gradient(135deg, #5b6470 0%, #4b5563 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.18) !important;
      }
    }
  }
}

/* Ocultar completamente el botón deny (NO) */
::ng-deep .swal2-deny {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Asegurar que solo aparezcan 2 botones */
::ng-deep .swal2-actions {
  .swal2-deny {
    display: none !important;
  }

  /* Solo mostrar confirm y cancel */
  .swal2-confirm,
  .swal2-cancel {
    display: inline-flex !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
}