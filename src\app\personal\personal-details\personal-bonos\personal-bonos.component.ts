import { Component, Input, OnChanges, OnInit, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotificationService } from '../../../servicios/notification.service';

export interface Bono {
  id_bono?: number;
  id_personal: number;
  tipo_bono: string;
  monto: number;
  fecha_pago: string;
  forma_pago: 'EFECTIVO' | 'TRANSFERENCIA' | 'CHEQUE';
  condicion: 'PAGADO' | 'PENDIENTE' | 'CANCELADO';
  ruta_archivo?: string;
  observaciones?: string;
  created_at?: string;
  updated_at?: string;
}

@Component({
  selector: 'app-personal-bonos',
  templateUrl: './personal-bonos.component.html',
  styleUrls: ['./personal-bonos.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class PersonalBonosComponent implements OnInit, OnChanges {
  @Input() personalId: string | null = null;
  @Input() personalData: any = null;

  // Propiedades para gestión de bonos
  bonos: Bono[] = [];
  isLoading: boolean = false;
  isSaving: boolean = false;
  isUploading: boolean = false;

  // Propiedades para nuevo bono
  nuevoBono: Bono = {
    id_personal: 0,
    tipo_bono: 'BONO DE PRODUCTIVIDAD',
    monto: 0,
    fecha_pago: '',
    forma_pago: 'TRANSFERENCIA',
    condicion: 'PENDIENTE',
    observaciones: ''
  };

  // Archivo seleccionado para subir
  selectedFile: File | null = null;

  // Propiedades para paginación
  currentPage: number = 1;
  itemsPerPage: number = 5;
  totalPages: number = 1;
  displayedBonos: Bono[] = [];

  // Propiedades para filtros
  filtroTipo: string = '';
  filtroCondicion: string = '';
  filtroFormaPago: string = '';
  filtroAnio: string = '';

  // Tipos de bonos predefinidos
  tiposBonos: string[] = [
    'BONO DE PRODUCTIVIDAD',
    'BONO POR DESEMPEÑO',
    'BONO DE PUNTUALIDAD',
    'BONO DE ASISTENCIA',
    'BONO NAVIDEÑO',
    'BONO DE VENTAS',
    'BONO EXTRAORDINARIO',
    'GRATIFICACIÓN',
    'OTRO'
  ];

  constructor(
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadBonos();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['personalId'] && this.personalId) {
      this.nuevoBono.id_personal = parseInt(this.personalId);
      this.loadBonos();
    }
  }

  // Cargar bonos del personal
  loadBonos(): void {
    if (!this.personalId) return;

    this.isLoading = true;
    // TODO: Implementar servicio de bonos
    // Por ahora simulamos datos
    setTimeout(() => {
      this.bonos = [
        {
          id_bono: 1,
          id_personal: parseInt(this.personalId!),
          tipo_bono: 'BONO DE PRODUCTIVIDAD',
          monto: 60.00,
          fecha_pago: '2025-07-01',
          forma_pago: 'TRANSFERENCIA',
          condicion: 'PAGADO',
          ruta_archivo: 'bonos/bono_productividad_julio_2025.pdf',
          observaciones: 'Bono por cumplimiento de metas del mes'
        },
        {
          id_bono: 2,
          id_personal: parseInt(this.personalId!),
          tipo_bono: 'BONO POR DESEMPEÑO',
          monto: 120.00,
          fecha_pago: '2025-06-15',
          forma_pago: 'TRANSFERENCIA',
          condicion: 'PAGADO',
          ruta_archivo: 'bonos/bono_desempeno_junio_2025.pdf',
          observaciones: 'Evaluación trimestral excelente'
        },
        {
          id_bono: 3,
          id_personal: parseInt(this.personalId!),
          tipo_bono: 'BONO NAVIDEÑO',
          monto: 300.00,
          fecha_pago: '2024-12-20',
          forma_pago: 'EFECTIVO',
          condicion: 'PAGADO',
          observaciones: 'Bono navideño anual'
        }
      ];
      this.updateDisplayedBonos();
      this.isLoading = false;
      this.cdr.detectChanges();
    }, 1000);
  }

  // Manejar selección de archivo
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validar tipo de archivo
      if (file.type !== 'application/pdf') {
        this.notificationService.error('Solo se permiten archivos PDF');
        return;
      }
      
      // Validar tamaño (máximo 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.notificationService.error('El archivo no debe superar los 5MB');
        return;
      }

      this.selectedFile = file;
      this.notificationService.success(`Archivo seleccionado: ${file.name}`);
    }
  }

  // Subir archivo
  uploadFile(): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.selectedFile) {
        reject('No hay archivo seleccionado');
        return;
      }

      this.isUploading = true;
      
      // Simular subida de archivo
      setTimeout(() => {
        const fileName = `bono_${Date.now()}_${this.selectedFile!.name}`;
        const filePath = `bonos/${fileName}`;
        
        // TODO: Implementar subida real al servidor
        console.log('Subiendo archivo:', this.selectedFile);
        console.log('Ruta destino:', filePath);
        
        this.isUploading = false;
        resolve(filePath);
      }, 2000);
    });
  }

  // Guardar nuevo bono
  async guardarBono(): Promise<void> {
    if (!this.validarBono()) {
      return;
    }

    this.isSaving = true;

    try {
      // Subir archivo si hay uno seleccionado
      if (this.selectedFile) {
        const rutaArchivo = await this.uploadFile();
        this.nuevoBono.ruta_archivo = rutaArchivo;
      }

      // TODO: Implementar servicio para guardar bono
      setTimeout(() => {
        this.bonos.unshift({ 
          ...this.nuevoBono, 
          id_bono: Date.now(),
          created_at: new Date().toISOString()
        });
        this.updateDisplayedBonos();
        this.resetForm();
        this.isSaving = false;
        this.notificationService.success('Bono registrado exitosamente');
        this.cdr.detectChanges();
      }, 1000);

    } catch (error) {
      this.isSaving = false;
      this.notificationService.error('Error al guardar el bono');
      console.error('Error:', error);
    }
  }

  // Validar bono
  validarBono(): boolean {
    if (!this.nuevoBono.tipo_bono.trim()) {
      this.notificationService.error('El tipo de bono es obligatorio');
      return false;
    }
    if (!this.nuevoBono.monto || this.nuevoBono.monto <= 0) {
      this.notificationService.error('El monto debe ser mayor a 0');
      return false;
    }
    if (!this.nuevoBono.fecha_pago) {
      this.notificationService.error('La fecha de pago es obligatoria');
      return false;
    }
    return true;
  }

  // Resetear formulario
  resetForm(): void {
    this.nuevoBono = {
      id_personal: parseInt(this.personalId || '0'),
      tipo_bono: 'BONO DE PRODUCTIVIDAD',
      monto: 0,
      fecha_pago: '',
      forma_pago: 'TRANSFERENCIA',
      condicion: 'PENDIENTE',
      observaciones: ''
    };
    this.selectedFile = null;
    
    // Limpiar input de archivo
    const fileInput = document.getElementById('archivo_bono') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  // Descargar archivo
  descargarArchivo(bono: Bono): void {
    if (!bono.ruta_archivo) {
      this.notificationService.error('No hay archivo disponible para descargar');
      return;
    }

    // TODO: Implementar descarga real
    console.log('Descargando archivo:', bono.ruta_archivo);
    this.notificationService.info('Descargando archivo...');
    
    // Simular descarga
    const link = document.createElement('a');
    link.href = `/public/${bono.ruta_archivo}`;
    link.download = `bono_${bono.tipo_bono}_${bono.fecha_pago}.pdf`;
    link.click();
  }

  // Actualizar bonos mostrados con filtros y paginación
  updateDisplayedBonos(): void {
    let filteredBonos = [...this.bonos];

    // Aplicar filtros
    if (this.filtroTipo) {
      filteredBonos = filteredBonos.filter(b => b.tipo_bono === this.filtroTipo);
    }
    if (this.filtroCondicion) {
      filteredBonos = filteredBonos.filter(b => b.condicion === this.filtroCondicion);
    }
    if (this.filtroFormaPago) {
      filteredBonos = filteredBonos.filter(b => b.forma_pago === this.filtroFormaPago);
    }
    if (this.filtroAnio) {
      filteredBonos = filteredBonos.filter(b => b.fecha_pago.startsWith(this.filtroAnio));
    }

    // Calcular paginación
    this.totalPages = Math.ceil(filteredBonos.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.displayedBonos = filteredBonos.slice(startIndex, endIndex);
  }

  // Métodos de paginación
  onPageSizeChange(newSize: number): void {
    this.itemsPerPage = newSize;
    this.currentPage = 1;
    this.updateDisplayedBonos();
  }

  goToPage(page: number): void {
    this.currentPage = page;
    this.updateDisplayedBonos();
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // Aplicar filtros
  aplicarFiltros(): void {
    this.currentPage = 1;
    this.updateDisplayedBonos();
  }

  // Limpiar filtros
  limpiarFiltros(): void {
    this.filtroTipo = '';
    this.filtroCondicion = '';
    this.filtroFormaPago = '';
    this.filtroAnio = '';
    this.currentPage = 1;
    this.updateDisplayedBonos();
  }

  // Obtener clase CSS para la condición
  getCondicionClass(condicion: string): string {
    switch (condicion) {
      case 'PAGADO': return 'bg-green-100 text-green-800';
      case 'PENDIENTE': return 'bg-yellow-100 text-yellow-800';
      case 'CANCELADO': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Obtener clase CSS para la forma de pago
  getFormaPagoClass(formaPago: string): string {
    switch (formaPago) {
      case 'TRANSFERENCIA': return 'bg-blue-100 text-blue-800';
      case 'EFECTIVO': return 'bg-green-100 text-green-800';
      case 'CHEQUE': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Formatear fecha
  formatearFecha(fecha: string): string {
    if (!fecha) return '';
    return new Date(fecha).toLocaleDateString('es-ES');
  }

  // Formatear monto
  formatearMonto(monto: number): string {
    return `S/ ${monto.toFixed(2)}`;
  }
}
