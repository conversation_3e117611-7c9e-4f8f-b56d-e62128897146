import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Empresa, EmpresaService } from '../servicios/empresa.service';
import { EmpresaFormModal } from './empresa-form-modal/empresa-form-modal';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { map, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-empresas',
  standalone: true,
  imports: [CommonModule, FormsModule, EmpresaFormModal],
  templateUrl: './empresas.component.html',
  styleUrls: ['./empresas.component.scss'],
  providers: [EmpresaService]
})


export class EmpresasComponent implements OnInit {
  isLoading: boolean = false;

  private _empresas = new BehaviorSubject<Empresa[]>([]);
  empresas$ = this._empresas.asObservable();

  filterText: string = '';
  private _filterText = new BehaviorSubject<string>('');

  currentPage: number = 1;
  private _currentPage = new BehaviorSubject<number>(1);

  itemsPerPage: number = 5;
  private _itemsPerPage = new BehaviorSubject<number>(5);

  pageSizeOptions: number[] = [5, 10, 20];
  isModalVisible: boolean = false;
  isEditMode: boolean = false;
  empresaToEdit: Empresa | null = null;

  displayedEmpresas$: Observable<Empresa[]>;
  totalPages$: Observable<number>;

  constructor(private empresaService: EmpresaService) {
    this.displayedEmpresas$ = combineLatest([
      this.empresas$,
      this._filterText.pipe(debounceTime(300), distinctUntilChanged()),
      this._currentPage,
      this._itemsPerPage
    ]).pipe(
      map(([empresas, filterText, currentPage, itemsPerPage]) => {
        const filtered = empresas.filter(empresa => {
          const ruc = empresa.ruc ?? '';
          const razon = empresa.razon_social ?? '';
          const direccion = empresa.direccion_empresa ?? '';
          const dep = empresa.departamento ?? '';
          const prov = empresa.provincia ?? '';
          const dist = empresa.distrito ?? '';
          const representante = this.getRepresentanteCompleto(empresa).toLowerCase();
          const search = filterText.toLowerCase();
          return (
            ruc.toLowerCase().includes(search) ||
            razon.toLowerCase().includes(search) ||
            direccion.toLowerCase().includes(search) ||
            dep.toLowerCase().includes(search) ||
            prov.toLowerCase().includes(search) ||
            dist.toLowerCase().includes(search) ||
            representante.includes(search)
          );
        });

        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filtered.slice(startIndex, endIndex);
      })
    );

    this.totalPages$ = combineLatest([
      this.empresas$,
      this._filterText.pipe(debounceTime(300), distinctUntilChanged()),
      this._itemsPerPage
    ]).pipe(
      map(([empresas, filterText, itemsPerPage]) => {
        const filtered = empresas.filter(empresa => {
          const ruc = empresa.ruc ?? '';
          const razon = empresa.razon_social ?? '';
          const direccion = empresa.direccion_empresa ?? '';
          const dep = empresa.departamento ?? '';
          const prov = empresa.provincia ?? '';
          const dist = empresa.distrito ?? '';
          const representante = this.getRepresentanteCompleto(empresa).toLowerCase();
          const search = filterText.toLowerCase();
          return (
            ruc.toLowerCase().includes(search) ||
            razon.toLowerCase().includes(search) ||
            direccion.toLowerCase().includes(search) ||
            dep.toLowerCase().includes(search) ||
            prov.toLowerCase().includes(search) ||
            dist.toLowerCase().includes(search) ||
            representante.includes(search)
          );
        });
        return Math.ceil(filtered.length / itemsPerPage);
      })
    );
  }

  ngOnInit(): void {
    this.loadEmpresas();
  }

  loadEmpresas(): void {
  this.isLoading = true;
  this.empresaService.getEmpresas().subscribe({
    next: (data) => {
      this._empresas.next(data);
      this.isLoading = false;
    },
    error: (error) => {
      console.error('Error al cargar empresas:', error);
      this.isLoading = false;
    }
  });
}


  onFilterTextChange(text: string): void {
    this.filterText = text;
    this._filterText.next(text);
    this._currentPage.next(1);
  }

  goToPage(page: number): void {
    this.currentPage = page;
    this._currentPage.next(page);
  }

  onPageSizeChange(event: Event): void {
    const newSize = +(event.target as HTMLSelectElement).value;
    this.itemsPerPage = newSize;
    this._itemsPerPage.next(newSize);
    this.currentPage = 1;
    this._currentPage.next(1);
  }

  openModal(): void {
    this.isEditMode = false;
    this.empresaToEdit = null;
    this.isModalVisible = true;
  }

  openEditModal(empresa: Empresa): void {
    this.isEditMode = true;
    this.empresaToEdit = { ...empresa };
    this.isModalVisible = true;
  }

  closeModal(): void {
    this.isModalVisible = false;
    this.empresaToEdit = null; // Limpiar la empresa a editar al cerrar
    console.log('Modal cerrado. isModalVisible:', this.isModalVisible);
  }

  getRepresentanteCompleto(empresa: Empresa): string {
    const dni = empresa.dni ?? '';
    const nombres = empresa.nombres ?? '';
    const apellidos = empresa.apellidos ?? '';
    return `${dni} - ${apellidos} ${nombres}`;
  }

  getPageNumbers(totalPages: number): number[] {
    return Array(totalPages).fill(0).map((_, i) => i + 1);
  }
}
