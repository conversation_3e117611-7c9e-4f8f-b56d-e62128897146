<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h2 class="text-lg font-bold font-poppins text-custom-blue mb-4">Gestión de Bonos</h2>
  </div>

  <!-- Nuevo Bono -->
  <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
    <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Nuevo Bono</h3>
    
    <form (ngSubmit)="guardarBono()" class="space-y-4">
      <!-- Tipo de Bono -->
      <div>
        <label for="tipo_bono" class="block text-sm font-medium text-gray-700">
          Tipo de Bono <span class="text-red-500">*</span>
        </label>
        <select
          id="tipo_bono"
          [(ngModel)]="nuevoBono.tipo_bono"
          name="tipo_bono"
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
          required>
          <option *ngFor="let tipo of tiposBonos" [value]="tipo">{{ tipo }}</option>
        </select>
      </div>

      <!-- Monto y Fecha de Pago -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="monto" class="block text-sm font-medium text-gray-700">
            Monto <span class="text-red-500">*</span>
          </label>
          <div class="mt-1 relative rounded-md shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gray-500 sm:text-sm">S/</span>
            </div>
            <input
              type="number"
              id="monto"
              [(ngModel)]="nuevoBono.monto"
              name="monto"
              step="0.01"
              min="0"
              placeholder="0.00"
              class="pl-8 mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              required>
          </div>
        </div>

        <div>
          <label for="fecha_pago" class="block text-sm font-medium text-gray-700">
            Fecha de Pago <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="fecha_pago"
            [(ngModel)]="nuevoBono.fecha_pago"
            name="fecha_pago"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>
      </div>

      <!-- Forma de Pago y Condición -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="forma_pago" class="block text-sm font-medium text-gray-700">
            Forma de Pago <span class="text-red-500">*</span>
          </label>
          <select
            id="forma_pago"
            [(ngModel)]="nuevoBono.forma_pago"
            name="forma_pago"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
            <option value="TRANSFERENCIA">Transferencia</option>
            <option value="EFECTIVO">Efectivo</option>
            <option value="CHEQUE">Cheque</option>
          </select>
        </div>

        <div>
          <label for="condicion" class="block text-sm font-medium text-gray-700">
            Condición <span class="text-red-500">*</span>
          </label>
          <select
            id="condicion"
            [(ngModel)]="nuevoBono.condicion"
            name="condicion"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
            <option value="PENDIENTE">Pendiente</option>
            <option value="PAGADO">Pagado</option>
            <option value="CANCELADO">Cancelado</option>
          </select>
        </div>
      </div>

      <!-- Archivo PDF -->
      <div>
        <label for="archivo_bono" class="block text-sm font-medium text-gray-700">
          Constancia PDF
        </label>
        <div class="mt-1 flex items-center space-x-3">
          <input
            type="file"
            id="archivo_bono"
            accept=".pdf"
            (change)="onFileSelected($event)"
            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
          <div *ngIf="selectedFile" class="flex items-center text-sm text-green-600">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            {{ selectedFile.name }}
          </div>
        </div>
        <p class="mt-1 text-xs text-gray-500">Solo archivos PDF, máximo 5MB</p>
      </div>

      <!-- Observaciones -->
      <div>
        <label for="observaciones" class="block text-sm font-medium text-gray-700">
          Observaciones
        </label>
        <textarea
          id="observaciones"
          [(ngModel)]="nuevoBono.observaciones"
          name="observaciones"
          rows="2"
          placeholder="Observaciones adicionales..."
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm resize-none"></textarea>
      </div>

      <!-- Botones -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          (click)="resetForm()"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Limpiar
        </button>
        <button
          type="submit"
          [disabled]="isSaving || isUploading"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="!isSaving && !isUploading">Registrar Bono</span>
          <span *ngIf="isUploading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Subiendo archivo...
          </span>
          <span *ngIf="isSaving && !isUploading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Guardando...
          </span>
        </button>
      </div>
    </form>
  </div>

  <!-- Historial de Bonos -->
  <div class="bg-white rounded-lg shadow-md border border-gray-200">
    <div class="p-6 border-b border-gray-200 bg-green-50">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
          </svg>
          <h3 class="text-lg font-bold text-green-800">Historial de Bonos</h3>
        </div>
        
        <!-- Filtros -->
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <select
            [(ngModel)]="filtroTipo"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los tipos</option>
            <option *ngFor="let tipo of tiposBonos" [value]="tipo">{{ tipo }}</option>
          </select>

          <select
            [(ngModel)]="filtroCondicion"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todas las condiciones</option>
            <option value="PAGADO">Pagado</option>
            <option value="PENDIENTE">Pendiente</option>
            <option value="CANCELADO">Cancelado</option>
          </select>

          <select
            [(ngModel)]="filtroFormaPago"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todas las formas</option>
            <option value="TRANSFERENCIA">Transferencia</option>
            <option value="EFECTIVO">Efectivo</option>
            <option value="CHEQUE">Cheque</option>
          </select>

          <select
            [(ngModel)]="filtroAnio"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los años</option>
            <option value="2025">2025</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
          </select>

          <button
            (click)="limpiarFiltros()"
            class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 focus:outline-none">
            Limpiar filtros
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div *ngIf="isLoading" class="p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-600">Cargando bonos...</span>
      </div>
    </div>

    <!-- Tabla de bonos -->
    <div *ngIf="!isLoading" class="overflow-x-auto">
      <table class="min-w-full divide-y divide-green-200">
        <thead class="bg-gradient-to-r from-green-100 to-emerald-100">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Tipo de Bono</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Monto</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Fecha de Pago</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Forma de Pago</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Condición</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">Acciones</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngIf="displayedBonos.length === 0">
            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
              <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                </svg>
                <p class="text-gray-500">No se encontraron bonos registrados</p>
              </div>
            </td>
          </tr>
          <tr *ngFor="let bono of displayedBonos; let i = index" 
              class="hover:bg-green-50 transition-colors duration-150"
              [class.bg-green-25]="i % 2 === 0">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8">
                  <div class="h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <div class="font-medium text-gray-900">{{ bono.tipo_bono }}</div>
                  <div class="text-gray-500 text-xs" *ngIf="bono.observaciones">{{ bono.observaciones | slice:0:30 }}{{ bono.observaciones.length > 30 ? '...' : '' }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                </svg>
                <span class="font-bold text-green-600">{{ formatearMonto(bono.monto) }}</span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                {{ formatearFecha(bono.fecha_pago) }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getFormaPagoClass(bono.forma_pago)">
                {{ bono.forma_pago }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getCondicionClass(bono.condicion)">
                {{ bono.condicion }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <div class="flex items-center space-x-2">
                <button
                  *ngIf="bono.ruta_archivo"
                  (click)="descargarArchivo(bono)"
                  class="text-indigo-600 hover:text-indigo-900 flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                  PDF
                </button>
                <span *ngIf="!bono.ruta_archivo" class="text-gray-400">Sin archivo</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div *ngIf="!isLoading && displayedBonos.length > 0" class="px-6 py-4 border-t border-gray-200">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700">Mostrar:</span>
          <select
            [(ngModel)]="itemsPerPage"
            (change)="onPageSizeChange(itemsPerPage)"
            class="border border-indigo-300 rounded px-2 py-1 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option [ngValue]="5">5</option>
            <option [ngValue]="10">10</option>
            <option [ngValue]="20">20</option>
          </select>
          <span class="text-sm text-gray-700">por página</span>
        </div>

        <div class="flex items-center space-x-2">
          <button
            (click)="goToPage(currentPage - 1)"
            [disabled]="currentPage === 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Anterior
          </button>

          <button
            *ngFor="let page of getPageNumbers()"
            (click)="goToPage(page)"
            [class.bg-blue-600]="page === currentPage"
            [class.text-white]="page === currentPage"
            [class.text-gray-700]="page !== currentPage"
            [class.hover:bg-gray-50]="page !== currentPage"
            class="px-3 py-1 border border-gray-300 rounded text-sm">
            {{ page }}
          </button>

          <button
            (click)="goToPage(currentPage + 1)"
            [disabled]="currentPage === totalPages"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Siguiente
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
