# 📁 Funcionalidad de Subir Archivos PDF - Contratos

## ✅ **Implementación Completada**

### **1. Estructura creada:**
```
📁 public/uploads/contratos/
└── (aquí se guardarán los PDFs)
```

### **2. Servidor de uploads:**
- **Archivo**: `upload-server.js`
- **Puerto**: 3001
- **Endpoint**: `http://localhost:3001/api/upload/contrato`

### **3. Cómo usar:**

#### **A. Iniciar servidores:**
```bash
# Opción 1: Ambos servidores a la vez
npm run dev

# Opción 2: Por separado
npm start                # Angular en puerto 4200
npm run upload-server    # Uploads en puerto 3001
```

#### **B. Subir archivo:**
1. **Ir a Contratos**: Navegar al módulo de contratos
2. **Click "Subir"**: En cualquier contrato de la tabla
3. **Seleccionar PDF**: Desde el explorador (máximo 10MB)
4. **Click "Subir Archivo"**: Se guarda automáticamente
5. **Verificar**: El archivo aparece en `public/uploads/contratos/`

### **4. Nomenclatura de archivos:**
```
Formato: DNI_FECHA_HORA_NOMBRES.pdf
Ejemplo: 74593277_2025-09-02T19-01-51_ABAD_CORONADO_KHATERIN_NATHALY.pdf
```

### **5. Validaciones:**
- ✅ **Tipo**: Solo archivos PDF
- ✅ **Tamaño**: Máximo 10MB
- ✅ **Nombre único**: Con DNI, fecha/hora y nombres

### **6. Logs en consola:**
```
📁 Guardando archivo en: public/uploads/contratos/[NOMBRE].pdf
📄 Archivo original: archivo-original.pdf
📊 Tamaño: 118.08 KB
✅ Archivo guardado exitosamente en: public/uploads/contratos/[NOMBRE].pdf
```

### **7. Características:**
- **Drag & Drop**: Arrastra archivos al modal
- **Validación inmediata**: Tipo y tamaño
- **Preview**: Muestra archivo seleccionado
- **Loading**: Spinner durante subida
- **Notificaciones**: Éxito y errores

### **8. Troubleshooting:**

#### **A. Error "Servidor no ejecutándose":**
```bash
# Solución: Iniciar servidor de uploads
npm run upload-server
```

#### **B. Error "Puerto ocupado":**
```bash
# Cambiar puerto en upload-server.js línea 6:
const PORT = 3002; // O cualquier otro puerto libre
```

#### **C. Carpeta no existe:**
```bash
# El servidor la crea automáticamente
# Verificar en: public/uploads/contratos/
```

## **🎯 Resultado Final:**
- **Modal funcional**: Subir archivos PDF
- **Guardado automático**: En carpeta del proyecto
- **Sin descargas**: No abre explorador adicional
- **Nombres únicos**: Con DNI, fecha/hora y nombres
- **Validaciones completas**: Tipo y tamaño
