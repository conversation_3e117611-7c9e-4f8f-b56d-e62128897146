<div class="space-y-6">
  <!-- Loading General -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div class="text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg text-gray-600">Cargando dashboard de vacaciones...</span>
      </div>
      <p class="text-sm text-gray-500 mt-2">Por favor espere mientras se cargan los datos</p>
    </div>
  </div>

  <!-- Contenido principal (oculto durante loading) -->
  <div *ngIf="!isLoading && dashboardData">
    <!-- Header con selector de año -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold font-poppins text-custom-blue">Dashboard de Vacaciones</h1>

      <!-- Selector de Año -->
      <div class="flex items-center bg-purple-100 border border-purple-200 rounded-lg px-3 py-2 shadow-sm">
        <svg class="w-4 h-4 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
        </svg>
        <span class="text-purple-800 text-sm font-medium mr-2">Año:</span>
        <select
          name="anio_seleccionado"
          [(ngModel)]="anioSeleccionado"
          (change)="onAnioChange()"
          class="bg-transparent text-purple-900 text-sm font-bold focus:outline-none cursor-pointer">
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024">2024</option>
          <option value="2025">2025</option>
          <option value="2026">2026</option>
          <option value="2027">2027</option>
        </select>
      </div>
    </div>

    <!-- Estadísticas generales -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Total Personal -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Personal</p>
            <p class="text-2xl font-bold text-gray-900">{{ dashboardData.total_personal }}</p>
          </div>
        </div>
      </div>

      <!-- Corresponden -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Sí Corresponden</p>
            <p class="text-2xl font-bold text-green-600">{{ dashboardData.total_corresponden }}</p>
          </div>
        </div>
      </div>

      <!-- No Corresponden -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">No Corresponden</p>
            <p class="text-2xl font-bold text-red-600">{{ dashboardData.total_no_corresponden }}</p>
          </div>
        </div>
      </div>

       <!-- Completadas -->
      <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completadas</p>
            <p class="text-2xl font-bold text-green-600">{{ dashboardData.total_completas }}</p>
          </div>
        </div>
      </div>

      <!-- Año Consultado -->
      <!--<div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Año Consultado</p>
            <p class="text-2xl font-bold text-purple-600">{{ dashboardData.anio_consultado }}</p>
          </div>
        </div>
      </div>-->
    </div>

    <!-- Totales por Sede -->
    <div class="bg-white rounded-lg shadow-md border border-gray-200 mb-6">
      <div class="p-6 border-b border-gray-200 bg-indigo-50">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-indigo-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-lg font-bold text-indigo-800">Distribución por Sede</h3>
        </div>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div *ngFor="let sede of getSedeKeys()" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span class="text-sm font-medium text-gray-700">{{ sede }}</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
              {{ dashboardData.totales_por_sede[sede] }} personas
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros y Tabla de Personal -->
    <div class="bg-white rounded-lg shadow-md border border-gray-200">
      <div class="p-6 border-b border-gray-200 bg-green-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <h3 class="text-lg font-bold text-green-800">Listado de Personal - Vacaciones {{ dashboardData.anio_consultado }}</h3>
          </div>

          <!-- Filtros -->
          <div class="flex items-center space-x-3">
            <!-- Filtro por Sede -->
            <select
              [(ngModel)]="filtroSede"
              (change)="aplicarFiltros()"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-green-500">
              <option value="">Todas las sedes</option>
              <option *ngFor="let sede of getSedesUnicas()" [value]="sede">{{ sede }}</option>
            </select>

            <!-- Filtro por Corresponde -->
            <select
              [(ngModel)]="filtroCorresponde"
              (change)="aplicarFiltros()"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-green-500">
              <option value="">Todos</option>
              <option value="SI CORRESPONDE">Sí Corresponde</option>
              <option value="NO CORRESPONDE">No Corresponde</option>
            </select>

            <!-- Botón limpiar filtros -->
            <button
              (click)="limpiarFiltros()"
              class="text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md border border-gray-300">
              Limpiar
            </button>
          </div>
        </div>
      </div>

      <!-- Tabla -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Personal</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Documento</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sede</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiempo Servicio</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Días</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Corresponde</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngIf="displayedPersonal.length === 0">
              <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                No se encontraron registros con los filtros aplicados
              </td>
            </tr>
            <tr *ngFor="let persona of displayedPersonal" class="hover:bg-gray-50">
              <!-- Personal -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                      <span class="text-sm font-medium text-indigo-800">
                        {{ persona.apellidos_nombres_completos.split(' ')[0].charAt(0) }}{{ persona.apellidos_nombres_completos.split(' ')[1] ? persona.apellidos_nombres_completos.split(' ')[1].charAt(0) : '' }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ persona.apellidos_nombres_completos }}</div>
                    <div class="text-sm text-gray-500">Ingreso: {{ persona.fecha_incorporacion | date:'dd/MM/yyyy' }}</div>
                  </div>
                </div>
              </td>

              <!-- Documento -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ persona.tipo_documento }}: {{ persona.documento }}</div>
              </td>

              <!-- Sede -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ persona.sede }}
                </span>
              </td>

              <!-- Tiempo Servicio -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ persona.tiempo_servicio }}
              </td>

              <!-- Días -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex space-x-2">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    A: {{ persona.dias_asignados }}
                  </span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    U: {{ persona.dias_utilizados }}
                  </span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    D: {{ persona.dias_disponibles }}
                  </span>
                </div>
              </td>

              <!-- Corresponde -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      [class.bg-green-100]="persona.vacaciones_de_ley === 'SI CORRESPONDE'"
                      [class.text-green-800]="persona.vacaciones_de_ley === 'SI CORRESPONDE'"
                      [class.bg-red-100]="persona.vacaciones_de_ley === 'NO CORRESPONDE'"
                      [class.text-red-800]="persona.vacaciones_de_ley === 'NO CORRESPONDE'">
                  {{ persona.vacaciones_de_ley }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Paginación -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            (click)="goToPage(currentPage - 1)"
            [disabled]="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Anterior
          </button>
          <button
            (click)="goToPage(currentPage + 1)"
            [disabled]="currentPage === totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Siguiente
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div class="flex items-center space-x-2">
            <p class="text-sm text-gray-700">
              Mostrando {{ (currentPage - 1) * itemsPerPage + 1 }} a {{ Math.min(currentPage * itemsPerPage, dashboardData.personal.length) }} de {{ dashboardData.personal.length }} resultados
            </p>
            <select
              [(ngModel)]="itemsPerPage"
              (change)="onPageSizeChange(itemsPerPage)"
              class="text-sm border border-gray-300 rounded-md px-2 py-1">
              <option [value]="5">5 por página</option>
              <option [value]="10">10 por página</option>
              <option [value]="25">25 por página</option>
              <option [value]="50">50 por página</option>
            </select>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                (click)="goToPage(currentPage - 1)"
                [disabled]="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </button>

              <button
                *ngFor="let page of getPageNumbers()"
                (click)="goToPage(page)"
                [class.bg-indigo-50]="page === currentPage"
                [class.border-indigo-500]="page === currentPage"
                [class.text-indigo-600]="page === currentPage"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                {{ page }}
              </button>

              <button
                (click)="goToPage(currentPage + 1)"
                [disabled]="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mensaje cuando no hay datos -->
  <div *ngIf="!isLoading && !dashboardData" class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No hay datos disponibles</h3>
    <p class="mt-1 text-sm text-gray-500">No se pudieron cargar los datos del dashboard.</p>
  </div>
</div>
