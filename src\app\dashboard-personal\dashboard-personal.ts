import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PersonalService } from '../servicios/personal.service';
import { NotificationService } from '../servicios/notification.service';

export interface PersonalGeneral {
  id_personal: number;
  razon_social: string;
  tipo_documento: string;
  documento: string;
  apellidos_nombres_completos: string;
  fecha_nacimiento: string;
  edad: number;
  cargo: string;
  sede: string;
  fecha_inicio: string | null;
  fecha_fin: string | null;
  ruta_archivo: string | null;
}

export interface DashboardPersonalData {
  anio_consultado: number;
  total_personal: number;
  total_por_sede: { [sede: string]: number };
  total_contratos_vigentes: number;
  total_contratos_vencidos: number;
  total_sin_contrato: number;
  total_sin_archivo: number;
  personal: PersonalGeneral[];
}

@Component({
  selector: 'app-dashboard-personal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './dashboard-personal.html',
  styleUrl: './dashboard-personal.scss'
})
export class DashboardPersonal implements OnInit {

  // Propiedades para el dashboard de personal
  dashboardData: DashboardPersonalData | null = null;
  anioSeleccionado: string = '2025';
  isLoading: boolean = false;

  // Propiedades para paginación
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 1;
  displayedPersonal: PersonalGeneral[] = [];

  // Propiedades para filtros
  filtroSede: string = '';
  filtroEstadoContrato: string = '';
  filtroContratoFirmado: string = '';

  // Referencia a Math para usar en el template
  Math = Math;

  constructor(
    private personalService: PersonalService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  // Cargar datos del dashboard
  loadDashboardData(): void {
    this.isLoading = true;
    this.dashboardData = null; // Limpiar datos anteriores
    const anio = parseInt(this.anioSeleccionado);

    console.log(`🔄 Cargando dashboard de personal para el año ${anio}`);
    console.log(`⏳ Estado loading: ${this.isLoading}`);

    // Timeout de seguridad para evitar loading infinito
    const timeoutId = setTimeout(() => {
      if (this.isLoading) {
        console.warn('⚠️ Timeout: Deteniendo loading después de 10 segundos');
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    }, 10000);

    this.personalService.getPersonalGeneral()
      .subscribe({
        next: (data: PersonalGeneral[]) => {
          clearTimeout(timeoutId); // Limpiar timeout si la respuesta llega
          console.log('✅ Datos del personal recibidos:', data);
          console.log(`📊 Cantidad de registros: ${data.length}`);

          if (data && data.length > 0) {
            this.procesarDatosPersonal(data, anio);
            console.log('✅ Datos procesados exitosamente');
            console.log('📈 Dashboard data:', this.dashboardData);
          } else {
            console.warn('⚠️ No se recibieron datos del personal');
            this.dashboardData = {
              anio_consultado: anio,
              total_personal: 0,
              total_por_sede: {},
              total_contratos_vigentes: 0,
              total_contratos_vencidos: 0,
              total_sin_contrato: 0,
              total_sin_archivo: 0,
              personal: []
            };
          }

          this.isLoading = false;
          console.log(`⏳ Estado loading después: ${this.isLoading}`);
          this.cdr.detectChanges();
        },
        error: (error) => {
          clearTimeout(timeoutId); // Limpiar timeout si hay error
          console.error('❌ Error al cargar dashboard personal:', error);
          this.notificationService.error(`Error al cargar dashboard: ${error.message || 'Error desconocido'}`);
          this.dashboardData = null;
          this.displayedPersonal = [];
          this.isLoading = false;
          console.log(`⏳ Estado loading en error: ${this.isLoading}`);
          this.cdr.detectChanges();
        }
      });
  }

  // Procesar datos del personal y generar métricas
  private procesarDatosPersonal(personal: PersonalGeneral[], anio: number): void {

    // MOSTRAR SIEMPRE TODO EL PERSONAL en la tabla
    const personalCompleto = personal;
    console.log(`📊 Personal total mostrado: ${personalCompleto.length}`);

    // Pero calcular métricas solo para los que estuvieron activos en el año
    const personalActivoEnAnio = personal.filter(p => this.estuvoActivoEnAnio(p, anio));
    console.log(`📊 Personal activo en ${anio}: ${personalActivoEnAnio.length} de ${personal.length}`);

    // Calcular métricas basándose en TODO el personal para la tabla
    const totalPorSede: { [sede: string]: number } = {};
    let contratosVigentes = 0;
    let contratosVencidos = 0;
    let sinContrato = 0;
    let sinArchivo = 0;

    // Contar por sede: TODOS los empleados
    personalCompleto.forEach(p => {
      totalPorSede[p.sede] = (totalPorSede[p.sede] || 0) + 1;
    });

    // Contar sin contrato: TODOS los empleados (no solo los activos en el año)
    personalCompleto.forEach(p => {
      if (!p.fecha_inicio || !p.fecha_fin) {
        sinContrato++;
        console.log(`👤 Sin contrato: ${p.apellidos_nombres_completos}`);
      }
    });

    // Calcular métricas de contratos vigentes/vencidos: Solo los activos en el año
    personalActivoEnAnio.forEach(p => {
      // Solo analizar contratos que tienen fechas (los sin fechas ya se contaron arriba)
      if (p.fecha_inicio && p.fecha_fin) {
        const fechaFin = new Date(p.fecha_fin);
        const fechaActual = new Date();

        // Normalizar fechas para comparar solo día/mes/año (sin hora)
        fechaFin.setHours(0, 0, 0, 0);
        fechaActual.setHours(0, 0, 0, 0);

        // Un contrato está vigente si su fecha de fin es mayor o igual a hoy
        // Un contrato está vencido si su fecha de fin es menor a hoy
        if (fechaFin < fechaActual) {
          contratosVencidos++;
          console.log(`❌ Contrato vencido: ${p.apellidos_nombres_completos} (fin: ${p.fecha_fin})`);
        } else {
          contratosVigentes++;
          console.log(`✅ Contrato vigente: ${p.apellidos_nombres_completos} (fin: ${p.fecha_fin})`);
        }
      }
    });

    // Contar sin archivo: TODOS los empleados
    personalCompleto.forEach(p => {
      if (!p.ruta_archivo) {
        sinArchivo++;
        console.log(`📄 Sin archivo: ${p.apellidos_nombres_completos}`);
      }
    });

    console.log(`📈 Métricas calculadas:`, {
      total_personal: personalCompleto.length,
      personal_activo_en_anio: personalActivoEnAnio.length,
      contratos_vigentes: contratosVigentes,
      contratos_vencidos: contratosVencidos,
      sin_contrato: sinContrato,
      sin_archivo: sinArchivo,
      por_sede: totalPorSede
    });

    this.dashboardData = {
      anio_consultado: anio,
      total_personal: personalCompleto.length, // TODOS los empleados
      total_por_sede: totalPorSede,
      total_contratos_vigentes: contratosVigentes, // Solo activos en el año
      total_contratos_vencidos: contratosVencidos, // Solo activos en el año
      total_sin_contrato: sinContrato, // TODOS los empleados sin fechas de contrato
      total_sin_archivo: sinArchivo, // TODOS los empleados
      personal: personalCompleto // TODOS los empleados para la tabla
    };

    this.updateDisplayedPersonal();
  }

  // Verificar si un empleado estuvo activo en el año consultado
  private estuvoActivoEnAnio(persona: PersonalGeneral, anio: number): boolean {
    // Si no tiene fecha_inicio, no estuvo activo (pero se muestra en tabla)
    if (!persona.fecha_inicio) {
      return false;
    }

    const fechaInicio = new Date(persona.fecha_inicio);
    const fechaFin = persona.fecha_fin ? new Date(persona.fecha_fin) : new Date(); // Si no tiene fecha_fin, usar hoy

    // El empleado estuvo activo en el año si:
    // - Empezó antes o durante el año Y
    // - Terminó después del inicio del año (o sigue activo)
    const inicioDelAnio = new Date(anio, 0, 1); // 1 de enero del año
    const finDelAnio = new Date(anio, 11, 31); // 31 de diciembre del año

    const estuvoActivo = fechaInicio <= finDelAnio && fechaFin >= inicioDelAnio;

    console.log(`📅 ${persona.apellidos_nombres_completos}: inicio=${persona.fecha_inicio}, fin=${persona.fecha_fin}, activo en ${anio}: ${estuvoActivo}`);

    return estuvoActivo;
  }

  // Cambio de año
  onAnioChange(): void {
    console.log(`📅 Cambio de año a: ${this.anioSeleccionado}`);

    // Resetear filtros al cambiar año
    this.filtroSede = '';
    this.filtroEstadoContrato = '';
    this.filtroContratoFirmado = '';
    this.currentPage = 1;

    // Cargar datos del nuevo año
    this.loadDashboardData();
  }

  // Actualizar personal mostrado con filtros y paginación
  updateDisplayedPersonal(): void {
    if (!this.dashboardData) {
      this.displayedPersonal = [];
      return;
    }

    let filteredPersonal = [...this.dashboardData.personal];

    // Aplicar filtros
    if (this.filtroSede) {
      filteredPersonal = filteredPersonal.filter(p => p.sede === this.filtroSede);
    }

    if (this.filtroEstadoContrato) {
      filteredPersonal = filteredPersonal.filter(p => {
        const estadoContrato = this.getEstadoContrato(p);
        return estadoContrato === this.filtroEstadoContrato;
      });
    }

    if (this.filtroContratoFirmado) {
      filteredPersonal = filteredPersonal.filter(p => {
        if (this.filtroContratoFirmado === 'CON_ARCHIVO') {
          return p.ruta_archivo !== null && p.ruta_archivo !== '';
        } else if (this.filtroContratoFirmado === 'SIN_ARCHIVO') {
          return !p.ruta_archivo;
        }
        return true;
      });
    }

    // Calcular paginación
    this.totalPages = Math.ceil(filteredPersonal.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.displayedPersonal = filteredPersonal.slice(startIndex, endIndex);
  }

  // Obtener estado del contrato considerando el año seleccionado
  getEstadoContrato(persona: PersonalGeneral): string {
    // Si no tiene fecha_inicio o fecha_fin, mostrar N/A (no estuvo activo en ningún año)
    if (!persona.fecha_inicio || !persona.fecha_fin) {
      return 'N/A';
    }

    // Si no estuvo activo en el año seleccionado, mostrar N/A
    if (this.dashboardData && !this.estuvoActivoEnAnio(persona, this.dashboardData.anio_consultado)) {
      return 'N/A';
    }

    // Calcular estado basándose en la fecha actual (no en el año seleccionado)
    const fechaFin = new Date(persona.fecha_fin);
    const fechaActual = new Date();

    // Normalizar fechas para comparar solo día/mes/año (sin hora)
    fechaFin.setHours(0, 0, 0, 0);
    fechaActual.setHours(0, 0, 0, 0);

    // Un contrato está vigente si su fecha de fin es mayor o igual a hoy
    // Un contrato está vencido si su fecha de fin es menor a hoy
    if (fechaFin < fechaActual) {
      return 'VENCIDO';
    } else {
      return 'VIGENTE';
    }
  }

  // Obtener clase CSS para el estado del contrato
  getEstadoContratoClass(persona: PersonalGeneral): string {
    const estado = this.getEstadoContrato(persona);
    switch (estado) {
      case 'VIGENTE': return 'bg-green-100 text-green-800';
      case 'VENCIDO': return 'bg-red-100 text-red-800';
      case 'NO TIENE CONTRATO': return 'bg-gray-100 text-gray-800';
      case 'N/A': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Obtener texto para la columna "Contrato Firmado"
  getContratoFirmadoTexto(persona: PersonalGeneral): string {
    // Si no tiene fecha_inicio o fecha_fin, siempre "Faltante"
    if (!persona.fecha_inicio || !persona.fecha_fin) {
      return '✗ Faltante';
    }

    // Si no estuvo activo en el año seleccionado, "Faltante"
    if (this.dashboardData && !this.estuvoActivoEnAnio(persona, this.dashboardData.anio_consultado)) {
      return '✗ Faltante';
    }

    // Si estuvo activo en el año, verificar si tiene archivo
    if (persona.ruta_archivo) {
      return '✓ Subido';
    } else {
      return '✗ Faltante';
    }
  }

  // Obtener clase CSS para la columna "Contrato Firmado"
  getContratoFirmadoClass(persona: PersonalGeneral): string {
    const texto = this.getContratoFirmadoTexto(persona);
    if (texto.includes('Subido')) {
      return 'bg-green-100 text-green-800';
    } else {
      return 'bg-red-100 text-red-800';
    }
  }

  // Métodos de paginación (copiados del dashboard de vacaciones)
  onPageSizeChange(newSize: number): void {
    this.itemsPerPage = newSize;
    this.currentPage = 1;
    this.updateDisplayedPersonal();
  }

  goToPage(page: number): void {
    this.currentPage = page;
    this.updateDisplayedPersonal();
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // Obtener sedes únicas para el filtro
  getSedesUnicas(): string[] {
    if (!this.dashboardData) return [];
    return Object.keys(this.dashboardData.total_por_sede).sort();
  }

  // Formatear fecha considerando si estuvo activo en el año
  formatearFecha(fecha: string | null, persona?: PersonalGeneral): string {
    // Si no hay fecha, siempre N/A
    if (!fecha) return 'N/A';

    // Si la persona no tiene fecha_inicio o fecha_fin, siempre N/A
    if (persona && (!persona.fecha_inicio || !persona.fecha_fin)) {
      return 'N/A';
    }

    // Si se proporciona persona, verificar si estuvo activo en el año
    if (persona && this.dashboardData) {
      const estuvoActivo = this.estuvoActivoEnAnio(persona, this.dashboardData.anio_consultado);
      if (!estuvoActivo) {
        return 'N/A'; // No estuvo activo en el año seleccionado
      }
    }

    return new Date(fecha).toLocaleDateString('es-ES');
  }
}
