-- =====================================================
-- SISTEMA COMPLETO DE RECURSOS HUMANOS - MIDAS SOLUTIONS CENTER
-- Script de creación completa de base de datos
-- Versión: 1.0
-- Fecha: 2025-08-25
-- =====================================================

-- Crear base de datos
CREATE DATABASE IF NOT EXISTS rrhh_midas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE rrhh_midas;

-- =====================================================
-- TABLA: empresas
-- Descripción: Información de las empresas del sistema
-- =====================================================

CREATE TABLE IF NOT EXISTS empresas (
    id_empresa INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Datos básicos de la empresa
    ruc VARCHAR(11) NOT NULL UNIQUE COMMENT 'RUC de la empresa (11 dígitos)',
    razon_social VARCHAR(255) NOT NULL COMMENT 'Razón social de la empresa',
    nombre_comercial VARCHAR(255) NULL COMMENT 'Nombre comercial',
    
    -- Datos de contacto
    telefono_empresa VARCHAR(20) NULL COMMENT 'Teléfono principal',
    correo_empresa VARCHAR(100) NULL COMMENT 'Correo electrónico',
    direccion_empresa TEXT NULL COMMENT 'Dirección completa',
    
    -- Ubicación (Ubigeo)
    departamento VARCHAR(100) NULL COMMENT 'Departamento',
    provincia VARCHAR(100) NULL COMMENT 'Provincia', 
    distrito VARCHAR(100) NULL COMMENT 'Distrito',
    
    -- Datos del representante legal
    dni_representante VARCHAR(8) NULL COMMENT 'DNI del representante',
    nombres_representante VARCHAR(255) NULL COMMENT 'Nombres del representante',
    apellido_paterno_representante VARCHAR(100) NULL COMMENT 'Apellido paterno',
    apellido_materno_representante VARCHAR(100) NULL COMMENT 'Apellido materno',
    partida_registral VARCHAR(50) NULL COMMENT 'Partida registral',
    
    -- Estado y auditoría
    estado ENUM('ACTIVO', 'INACTIVO') DEFAULT 'ACTIVO',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_ruc (ruc),
    INDEX idx_razon_social (razon_social),
    INDEX idx_estado (estado),
    INDEX idx_departamento (departamento)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Empresas del sistema';

-- =====================================================
-- TABLA: personal
-- Descripción: Información del personal/empleados
-- =====================================================

CREATE TABLE IF NOT EXISTS personal (
    id_personal INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con empresa
    id_empresa INT NOT NULL,
    
    -- Datos personales básicos
    documento VARCHAR(8) NOT NULL UNIQUE COMMENT 'DNI del empleado',
    primer_nombre VARCHAR(100) NOT NULL COMMENT 'Primer nombre',
    segundo_nombre VARCHAR(100) NULL COMMENT 'Segundo nombre',
    apellido_paterno VARCHAR(100) NOT NULL COMMENT 'Apellido paterno',
    apellido_materno VARCHAR(100) NOT NULL COMMENT 'Apellido materno',
    
    -- Datos adicionales
    fecha_nacimiento DATE NULL COMMENT 'Fecha de nacimiento',
    edad INT NULL COMMENT 'Edad calculada',
    genero ENUM('MASCULINO', 'FEMENINO', 'OTRO') NULL COMMENT 'Género',
    estado_civil ENUM('SOLTERO', 'CASADO', 'DIVORCIADO', 'VIUDO', 'CONVIVIENTE') NULL COMMENT 'Estado civil',
    
    -- Datos de contacto
    telefono VARCHAR(20) NULL COMMENT 'Teléfono personal',
    email VARCHAR(100) NULL COMMENT 'Correo electrónico',
    direccion TEXT NULL COMMENT 'Dirección de domicilio',
    
    -- Ubicación
    departamento VARCHAR(100) NULL COMMENT 'Departamento de residencia',
    provincia VARCHAR(100) NULL COMMENT 'Provincia de residencia',
    distrito VARCHAR(100) NULL COMMENT 'Distrito de residencia',
    
    -- Datos laborales
    cargo VARCHAR(255) NULL COMMENT 'Cargo que desempeña',
    fecha_ingreso DATE NULL COMMENT 'Fecha de ingreso a la empresa',
    salario DECIMAL(10,2) NULL COMMENT 'Salario actual',
    
    -- Estado y auditoría
    estado ENUM('ACTIVO', 'INACTIVO', 'SUSPENDIDO') DEFAULT 'ACTIVO',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_documento (documento),
    INDEX idx_empresa (id_empresa),
    INDEX idx_nombres (primer_nombre, apellido_paterno, apellido_materno),
    INDEX idx_cargo (cargo),
    INDEX idx_estado (estado),
    
    -- Clave foránea
    FOREIGN KEY (id_empresa) REFERENCES empresas(id_empresa) ON DELETE RESTRICT ON UPDATE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Personal/Empleados del sistema';

-- =====================================================
-- TABLA: contratos
-- Descripción: Contratos laborales del personal
-- =====================================================

CREATE TABLE IF NOT EXISTS contratos (
    id_contrato INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con personal
    id_personal INT NOT NULL,
    
    -- Datos del empleador (de la empresa)
    empleador_razon_social VARCHAR(255) NOT NULL,
    empleador_ruc VARCHAR(11) NOT NULL,
    empleador_domicilio TEXT NOT NULL,
    empleador_sede VARCHAR(100) DEFAULT 'CHICLAYO',
    empleador_departamento VARCHAR(100) NOT NULL,
    empleador_provincia VARCHAR(100) NOT NULL,
    empleador_distrito VARCHAR(100) NOT NULL,
    
    -- Datos del empleado
    empleado_dni VARCHAR(8) NOT NULL,
    empleado_nombres VARCHAR(255) NOT NULL,
    genero ENUM('MASCULINO', 'FEMENINO') NOT NULL,
    empleado_domicilio TEXT NOT NULL,
    empleado_departamento VARCHAR(100) NOT NULL,
    empleado_provincia VARCHAR(100) NOT NULL,
    empleado_distrito VARCHAR(100) NOT NULL,
    empleado_cargo VARCHAR(255) NOT NULL,
    empleado_cantidad_ventas INT NULL COMMENT 'Para contratos comerciales',
    
    -- Datos del contrato
    fecha_inicio DATE NOT NULL COMMENT 'Fecha de inicio del contrato',
    fecha_fin DATE NOT NULL COMMENT 'Fecha de fin del contrato',
    categoria_contrato ENUM('COMERCIAL', 'ADMINISTRATIVO', 'OPERATIVO', 'GERENCIAL') NOT NULL,
    sueldo DECIMAL(10,2) NOT NULL COMMENT 'Sueldo mensual',
    numero_letras VARCHAR(500) NOT NULL COMMENT 'Sueldo en letras',
    importe DECIMAL(10,2) NULL COMMENT 'Importe adicional si aplica',
    importe_letras VARCHAR(500) NULL COMMENT 'Importe adicional en letras',
    fecha_firma VARCHAR(100) NOT NULL COMMENT 'Fecha de firma en formato texto',
    
    -- Datos de contacto
    email VARCHAR(100) NULL COMMENT 'Email del empleado',
    empleado_telefono VARCHAR(20) NULL COMMENT 'Teléfono del empleado',
    
    -- Estado y auditoría
    estado ENUM('A', 'I', 'V') DEFAULT 'A' COMMENT 'A=Activo, I=Inactivo, V=Vencido',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_personal (id_personal),
    INDEX idx_empleado_dni (empleado_dni),
    INDEX idx_fecha_inicio (fecha_inicio),
    INDEX idx_fecha_fin (fecha_fin),
    INDEX idx_categoria (categoria_contrato),
    INDEX idx_estado (estado),
    
    -- Restricciones
    CONSTRAINT chk_fechas_contrato CHECK (fecha_fin >= fecha_inicio),
    CONSTRAINT chk_sueldo_positivo CHECK (sueldo > 0),
    
    -- Clave foránea
    FOREIGN KEY (id_personal) REFERENCES personal(id_personal) ON DELETE CASCADE ON UPDATE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Contratos laborales';

-- =====================================================
-- TABLA: vacaciones
-- Descripción: Solicitudes de vacaciones del personal
-- =====================================================

CREATE TABLE IF NOT EXISTS vacaciones (
    id_vacacion INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con personal
    id_personal INT NOT NULL,
    
    -- Fechas de vacaciones
    fecha_inicio DATE NOT NULL COMMENT 'Fecha de inicio de las vacaciones',
    fecha_fin DATE NOT NULL COMMENT 'Fecha de fin de las vacaciones',
    
    -- Información de días
    dias_solicitados INT NOT NULL COMMENT 'Número de días solicitados',
    dias_disponibles INT NOT NULL DEFAULT 30 COMMENT 'Días disponibles del empleado',
    
    -- Estado de la solicitud
    estado ENUM('PENDIENTE', 'APROBADO', 'RECHAZADO') NOT NULL DEFAULT 'PENDIENTE',
    
    -- Información de la solicitud
    motivo VARCHAR(500) NOT NULL COMMENT 'Motivo de la solicitud',
    observaciones TEXT NULL COMMENT 'Observaciones adicionales',
    
    -- Fechas de gestión
    fecha_solicitud DATE NOT NULL COMMENT 'Fecha de la solicitud',
    fecha_respuesta DATETIME NULL COMMENT 'Fecha de respuesta',
    
    -- Información de aprobación
    aprobado_por VARCHAR(255) NULL COMMENT 'Quien aprobó o rechazó',
    id_aprobador INT NULL COMMENT 'ID del aprobador',
    motivo_rechazo TEXT NULL COMMENT 'Motivo del rechazo',
    
    -- Auditoría
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_personal_vacaciones (id_personal),
    INDEX idx_estado_vacaciones (estado),
    INDEX idx_fecha_inicio_vacaciones (fecha_inicio),
    INDEX idx_fecha_solicitud (fecha_solicitud),
    
    -- Restricciones
    CONSTRAINT chk_fechas_vacaciones CHECK (fecha_fin >= fecha_inicio),
    CONSTRAINT chk_dias_positivos CHECK (dias_solicitados > 0),
    
    -- Clave foránea
    FOREIGN KEY (id_personal) REFERENCES personal(id_personal) ON DELETE CASCADE ON UPDATE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Solicitudes de vacaciones';

-- =====================================================
-- TABLA: configuracion_vacaciones
-- Descripción: Configuración de políticas de vacaciones
-- =====================================================

CREATE TABLE IF NOT EXISTS configuracion_vacaciones (
    id_config INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Configuración general
    dias_anuales_default INT NOT NULL DEFAULT 30,
    dias_minimos_solicitud INT NOT NULL DEFAULT 1,
    dias_maximos_solicitud INT NOT NULL DEFAULT 30,
    dias_anticipacion_minima INT NOT NULL DEFAULT 15,
    
    -- Configuración de fechas
    permite_fines_semana BOOLEAN DEFAULT TRUE,
    permite_festivos BOOLEAN DEFAULT TRUE,
    
    -- Configuración de aprobación
    requiere_aprobacion BOOLEAN DEFAULT TRUE,
    auto_aprobar_hasta_dias INT NULL,
    
    -- Configuración específica
    id_empresa INT NULL,
    departamento VARCHAR(100) NULL,
    
    -- Estado
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_empresa_config (id_empresa),
    FOREIGN KEY (id_empresa) REFERENCES empresas(id_empresa) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Configuración de vacaciones';

-- =====================================================
-- TABLA: saldo_vacaciones
-- Descripción: Control de saldo de días de vacaciones
-- =====================================================

CREATE TABLE IF NOT EXISTS saldo_vacaciones (
    id_saldo INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con empleado
    id_personal INT NOT NULL,
    anio INT NOT NULL,
    
    -- Saldos
    dias_asignados INT NOT NULL DEFAULT 30,
    dias_utilizados INT NOT NULL DEFAULT 0,
    dias_pendientes INT NOT NULL DEFAULT 0,
    dias_disponibles INT GENERATED ALWAYS AS (dias_asignados - dias_utilizados - dias_pendientes) STORED,
    dias_vencidos INT NOT NULL DEFAULT 0,
    fecha_vencimiento DATE NULL,
    
    -- Auditoría
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices y restricciones
    UNIQUE KEY uk_personal_anio (id_personal, anio),
    INDEX idx_anio_saldo (anio),
    CONSTRAINT chk_saldo_coherente CHECK (dias_utilizados + dias_pendientes <= dias_asignados),
    
    FOREIGN KEY (id_personal) REFERENCES personal(id_personal) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Saldo de vacaciones por empleado';

-- =====================================================
-- TABLA: usuarios
-- Descripción: Usuarios del sistema para login y permisos
-- =====================================================

CREATE TABLE IF NOT EXISTS usuarios (
    id_usuario INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con personal (opcional)
    id_personal INT NULL,
    
    -- Datos de acceso
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    
    -- Información del usuario
    nombres VARCHAR(255) NOT NULL,
    apellidos VARCHAR(255) NOT NULL,
    
    -- Roles y permisos
    rol ENUM('ADMIN', 'RRHH', 'GERENTE', 'EMPLEADO') NOT NULL DEFAULT 'EMPLEADO',
    permisos JSON NULL COMMENT 'Permisos específicos en formato JSON',
    
    -- Estado y fechas
    activo BOOLEAN DEFAULT TRUE,
    ultimo_acceso TIMESTAMP NULL,
    fecha_expiracion DATE NULL,
    
    -- Auditoría
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_rol (rol),
    INDEX idx_activo (activo),
    
    FOREIGN KEY (id_personal) REFERENCES personal(id_personal) ON DELETE SET NULL
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Usuarios del sistema';

-- =====================================================
-- TABLA: auditoria
-- Descripción: Log de auditoría para cambios importantes
-- =====================================================

CREATE TABLE IF NOT EXISTS auditoria (
    id_auditoria INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Información del cambio
    tabla VARCHAR(100) NOT NULL,
    id_registro INT NOT NULL,
    accion ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    
    -- Datos del cambio
    datos_anteriores JSON NULL,
    datos_nuevos JSON NULL,
    campos_modificados JSON NULL,
    
    -- Usuario que realizó el cambio
    id_usuario INT NULL,
    usuario_nombre VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    -- Fecha y hora
    fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_tabla (tabla),
    INDEX idx_id_registro (id_registro),
    INDEX idx_accion (accion),
    INDEX idx_usuario_audit (id_usuario),
    INDEX idx_fecha_hora (fecha_hora),
    
    FOREIGN KEY (id_usuario) REFERENCES usuarios(id_usuario) ON DELETE SET NULL

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Log de auditoría del sistema';

-- =====================================================
-- VISTAS ÚTILES
-- =====================================================

-- Vista completa de personal con empresa
CREATE OR REPLACE VIEW vista_personal_completo AS
SELECT
    p.id_personal,
    p.documento,
    CONCAT(p.primer_nombre,
           CASE WHEN p.segundo_nombre IS NOT NULL THEN CONCAT(' ', p.segundo_nombre) ELSE '' END,
           ' ', p.apellido_paterno, ' ', p.apellido_materno) AS nombre_completo,
    p.primer_nombre,
    p.segundo_nombre,
    p.apellido_paterno,
    p.apellido_materno,
    p.fecha_nacimiento,
    p.edad,
    p.genero,
    p.estado_civil,
    p.telefono,
    p.email,
    p.direccion,
    CONCAT(p.departamento, ' - ', p.provincia, ' - ', p.distrito) AS ubicacion_completa,
    p.cargo,
    p.fecha_ingreso,
    p.salario,
    p.estado AS estado_personal,
    -- Datos de la empresa
    e.id_empresa,
    e.ruc,
    e.razon_social,
    e.nombre_comercial,
    e.telefono_empresa,
    e.correo_empresa,
    -- Fechas
    p.created_at,
    p.updated_at
FROM personal p
INNER JOIN empresas e ON p.id_empresa = e.id_empresa
ORDER BY p.apellido_paterno, p.apellido_materno, p.primer_nombre;

-- Vista de contratos activos
CREATE OR REPLACE VIEW vista_contratos_activos AS
SELECT
    c.id_contrato,
    c.id_personal,
    vpc.nombre_completo,
    vpc.documento,
    c.empleador_razon_social,
    c.empleado_cargo,
    c.fecha_inicio,
    c.fecha_fin,
    c.categoria_contrato,
    c.sueldo,
    c.estado,
    DATEDIFF(c.fecha_fin, CURDATE()) AS dias_restantes,
    CASE
        WHEN c.fecha_fin < CURDATE() THEN 'VENCIDO'
        WHEN DATEDIFF(c.fecha_fin, CURDATE()) <= 30 THEN 'POR_VENCER'
        ELSE 'VIGENTE'
    END AS estado_vigencia,
    c.created_at
FROM contratos c
INNER JOIN vista_personal_completo vpc ON c.id_personal = vpc.id_personal
WHERE c.estado = 'A'
ORDER BY c.fecha_fin ASC;

-- Vista de vacaciones completa
CREATE OR REPLACE VIEW vista_vacaciones_completa AS
SELECT
    v.id_vacacion,
    v.id_personal,
    vpc.nombre_completo,
    vpc.documento,
    vpc.cargo,
    v.fecha_inicio,
    v.fecha_fin,
    v.dias_solicitados,
    v.estado,
    v.motivo,
    v.observaciones,
    v.fecha_solicitud,
    v.fecha_respuesta,
    v.aprobado_por,
    v.motivo_rechazo,
    DATEDIFF(v.fecha_fin, v.fecha_inicio) + 1 AS dias_calculados,
    CASE
        WHEN v.estado = 'PENDIENTE' THEN 'Esperando aprobación'
        WHEN v.estado = 'APROBADO' THEN 'Vacaciones aprobadas'
        WHEN v.estado = 'RECHAZADO' THEN 'Solicitud rechazada'
    END AS estado_descripcion,
    v.created_at,
    v.updated_at
FROM vacaciones v
INNER JOIN vista_personal_completo vpc ON v.id_personal = vpc.id_personal
ORDER BY v.created_at DESC;

-- Vista resumen de vacaciones por empleado
CREATE OR REPLACE VIEW vista_resumen_vacaciones AS
SELECT
    sv.id_personal,
    vpc.nombre_completo,
    vpc.documento,
    sv.anio,
    sv.dias_asignados,
    sv.dias_utilizados,
    sv.dias_pendientes,
    sv.dias_disponibles,
    sv.dias_vencidos,
    COUNT(v.id_vacacion) as total_solicitudes,
    SUM(CASE WHEN v.estado = 'APROBADO' THEN 1 ELSE 0 END) as solicitudes_aprobadas,
    SUM(CASE WHEN v.estado = 'PENDIENTE' THEN 1 ELSE 0 END) as solicitudes_pendientes,
    SUM(CASE WHEN v.estado = 'RECHAZADO' THEN 1 ELSE 0 END) as solicitudes_rechazadas
FROM saldo_vacaciones sv
INNER JOIN vista_personal_completo vpc ON sv.id_personal = vpc.id_personal
LEFT JOIN vacaciones v ON sv.id_personal = v.id_personal AND YEAR(v.fecha_inicio) = sv.anio
GROUP BY sv.id_personal, sv.anio, vpc.nombre_completo, vpc.documento
ORDER BY sv.anio DESC, vpc.nombre_completo;

-- =====================================================
-- PROCEDIMIENTOS ALMACENADOS
-- =====================================================

DELIMITER //

-- Procedimiento para crear un nuevo empleado
CREATE PROCEDURE CrearEmpleado(
    IN p_id_empresa INT,
    IN p_documento VARCHAR(8),
    IN p_primer_nombre VARCHAR(100),
    IN p_segundo_nombre VARCHAR(100),
    IN p_apellido_paterno VARCHAR(100),
    IN p_apellido_materno VARCHAR(100),
    IN p_fecha_nacimiento DATE,
    IN p_genero ENUM('MASCULINO', 'FEMENINO', 'OTRO'),
    IN p_telefono VARCHAR(20),
    IN p_email VARCHAR(100),
    IN p_cargo VARCHAR(255),
    IN p_salario DECIMAL(10,2)
)
BEGIN
    DECLARE v_edad INT;
    DECLARE v_id_personal INT;

    -- Calcular edad
    SET v_edad = TIMESTAMPDIFF(YEAR, p_fecha_nacimiento, CURDATE());

    -- Insertar empleado
    INSERT INTO personal (
        id_empresa, documento, primer_nombre, segundo_nombre,
        apellido_paterno, apellido_materno, fecha_nacimiento, edad,
        genero, telefono, email, cargo, salario, fecha_ingreso
    ) VALUES (
        p_id_empresa, p_documento, p_primer_nombre, p_segundo_nombre,
        p_apellido_paterno, p_apellido_materno, p_fecha_nacimiento, v_edad,
        p_genero, p_telefono, p_email, p_cargo, p_salario, CURDATE()
    );

    SET v_id_personal = LAST_INSERT_ID();

    -- Crear saldo de vacaciones para el año actual
    INSERT INTO saldo_vacaciones (id_personal, anio, dias_asignados)
    VALUES (v_id_personal, YEAR(CURDATE()), 30);

    SELECT v_id_personal as id_personal, 'Empleado creado exitosamente' as mensaje;
END //

-- Procedimiento para aprobar vacaciones
CREATE PROCEDURE AprobarVacaciones(
    IN p_id_vacacion INT,
    IN p_aprobado_por VARCHAR(255),
    IN p_id_aprobador INT
)
BEGIN
    DECLARE v_id_personal INT;
    DECLARE v_anio INT;
    DECLARE v_dias_solicitados INT;
    DECLARE v_estado VARCHAR(20);

    -- Obtener datos de la solicitud
    SELECT id_personal, YEAR(fecha_inicio), dias_solicitados, estado
    INTO v_id_personal, v_anio, v_dias_solicitados, v_estado
    FROM vacaciones
    WHERE id_vacacion = p_id_vacacion;

    IF v_id_personal IS NOT NULL AND v_estado = 'PENDIENTE' THEN
        -- Actualizar la solicitud
        UPDATE vacaciones
        SET estado = 'APROBADO',
            fecha_respuesta = NOW(),
            aprobado_por = p_aprobado_por,
            id_aprobador = p_id_aprobador
        WHERE id_vacacion = p_id_vacacion;

        -- Actualizar saldo de vacaciones
        UPDATE saldo_vacaciones
        SET dias_utilizados = dias_utilizados + v_dias_solicitados,
            dias_pendientes = dias_pendientes - v_dias_solicitados
        WHERE id_personal = v_id_personal AND anio = v_anio;

        SELECT 'Vacaciones aprobadas correctamente' as mensaje;
    ELSE
        SELECT 'Solicitud no encontrada o ya procesada' as mensaje;
    END IF;
END //

-- Procedimiento para rechazar vacaciones
CREATE PROCEDURE RechazarVacaciones(
    IN p_id_vacacion INT,
    IN p_aprobado_por VARCHAR(255),
    IN p_id_aprobador INT,
    IN p_motivo_rechazo TEXT
)
BEGIN
    DECLARE v_id_personal INT;
    DECLARE v_anio INT;
    DECLARE v_dias_solicitados INT;
    DECLARE v_estado VARCHAR(20);

    -- Obtener datos de la solicitud
    SELECT id_personal, YEAR(fecha_inicio), dias_solicitados, estado
    INTO v_id_personal, v_anio, v_dias_solicitados, v_estado
    FROM vacaciones
    WHERE id_vacacion = p_id_vacacion;

    IF v_id_personal IS NOT NULL AND v_estado = 'PENDIENTE' THEN
        -- Actualizar la solicitud
        UPDATE vacaciones
        SET estado = 'RECHAZADO',
            fecha_respuesta = NOW(),
            aprobado_por = p_aprobado_por,
            id_aprobador = p_id_aprobador,
            motivo_rechazo = p_motivo_rechazo
        WHERE id_vacacion = p_id_vacacion;

        -- Liberar días pendientes
        UPDATE saldo_vacaciones
        SET dias_pendientes = dias_pendientes - v_dias_solicitados
        WHERE id_personal = v_id_personal AND anio = v_anio;

        SELECT 'Vacaciones rechazadas correctamente' as mensaje;
    ELSE
        SELECT 'Solicitud no encontrada o ya procesada' as mensaje;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- TRIGGERS PARA AUDITORÍA Y CONSISTENCIA
-- =====================================================

DELIMITER //

-- Trigger para auditoría en tabla personal
CREATE TRIGGER tr_personal_audit_update
AFTER UPDATE ON personal
FOR EACH ROW
BEGIN
    INSERT INTO auditoria (tabla, id_registro, accion, datos_anteriores, datos_nuevos, usuario_nombre)
    VALUES (
        'personal',
        NEW.id_personal,
        'UPDATE',
        JSON_OBJECT(
            'documento', OLD.documento,
            'primer_nombre', OLD.primer_nombre,
            'apellido_paterno', OLD.apellido_paterno,
            'cargo', OLD.cargo,
            'salario', OLD.salario,
            'estado', OLD.estado
        ),
        JSON_OBJECT(
            'documento', NEW.documento,
            'primer_nombre', NEW.primer_nombre,
            'apellido_paterno', NEW.apellido_paterno,
            'cargo', NEW.cargo,
            'salario', NEW.salario,
            'estado', NEW.estado
        ),
        USER()
    );
END //

-- Trigger para actualizar saldo al crear solicitud de vacaciones
CREATE TRIGGER tr_vacaciones_insert
AFTER INSERT ON vacaciones
FOR EACH ROW
BEGIN
    IF NEW.estado = 'PENDIENTE' THEN
        -- Actualizar días pendientes
        INSERT INTO saldo_vacaciones (id_personal, anio, dias_asignados, dias_pendientes)
        VALUES (NEW.id_personal, YEAR(NEW.fecha_inicio), 30, NEW.dias_solicitados)
        ON DUPLICATE KEY UPDATE
        dias_pendientes = dias_pendientes + NEW.dias_solicitados;
    END IF;
END //

-- Trigger para validar fechas de contrato
CREATE TRIGGER tr_contratos_validate
BEFORE INSERT ON contratos
FOR EACH ROW
BEGIN
    IF NEW.fecha_fin <= NEW.fecha_inicio THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'La fecha de fin debe ser posterior a la fecha de inicio';
    END IF;

    IF NEW.sueldo <= 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'El sueldo debe ser mayor a cero';
    END IF;
END //

DELIMITER ;

-- =====================================================
-- DATOS DE EJEMPLO
-- =====================================================

-- Insertar empresa de ejemplo
INSERT INTO empresas (
    ruc, razon_social, nombre_comercial, telefono_empresa, correo_empresa,
    direccion_empresa, departamento, provincia, distrito,
    dni_representante, nombres_representante, apellido_paterno_representante, apellido_materno_representante
) VALUES (
    '20605357734',
    'MIDAS SOLUTIONS CENTER E.I.R.L.',
    'MIDAS SOLUTIONS',
    '074-123456',
    '<EMAIL>',
    'ALFREDO MENDIOLA # 6315',
    'LIMA',
    'LIMA',
    'LOS OLIVOS',
    '12345678',
    'JUAN CARLOS',
    'GARCIA',
    'LOPEZ'
);

-- Insertar personal de ejemplo
INSERT INTO personal (
    id_empresa, documento, primer_nombre, segundo_nombre, apellido_paterno, apellido_materno,
    fecha_nacimiento, edad, genero, estado_civil, telefono, email, direccion,
    departamento, provincia, distrito, cargo, fecha_ingreso, salario
) VALUES
(1, '43728495', 'EBER', 'FERNANDO', 'VALLEJOS', 'MANAYAY',
 '1990-05-15', 34, 'MASCULINO', 'SOLTERO', '987654321', '<EMAIL>',
 'CALLE VENEZUELA 240 U.VECINAL VICTOR R. HAYA DE LA TORRE',
 'LAMBAYEQUE', 'FERREÑAFE', 'FERREÑAFE', 'ASESOR', '2024-01-15', 1130.00),

(1, '87654321', 'MARIA', 'ELENA', 'RODRIGUEZ', 'SILVA',
 '1985-08-22', 39, 'FEMENINO', 'CASADO', '987123456', '<EMAIL>',
 'AV. PRINCIPAL 123 URB. LAS FLORES',
 'LIMA', 'LIMA', 'SAN MIGUEL', 'GERENTE RRHH', '2023-03-01', 2500.00),

(1, '11223344', 'CARLOS', 'ALBERTO', 'MENDOZA', 'TORRES',
 '1992-12-10', 31, 'MASCULINO', 'CONVIVIENTE', '965432187', '<EMAIL>',
 'JR. LOS PINOS 456 PUEBLO JOVEN ESPERANZA',
 'LAMBAYEQUE', 'CHICLAYO', 'CHICLAYO', 'DESARROLLADOR', '2024-02-01', 1800.00);

-- Insertar configuración de vacaciones
INSERT INTO configuracion_vacaciones (
    dias_anuales_default, dias_minimos_solicitud, dias_maximos_solicitud,
    dias_anticipacion_minima, requiere_aprobacion
) VALUES (30, 1, 15, 15, TRUE);

-- Insertar saldos de vacaciones
INSERT INTO saldo_vacaciones (id_personal, anio, dias_asignados, dias_utilizados, dias_pendientes) VALUES
(1, 2024, 30, 15, 0),
(1, 2023, 30, 30, 0),
(2, 2024, 30, 10, 5),
(2, 2023, 30, 25, 0),
(3, 2024, 30, 0, 8);

-- Insertar contratos de ejemplo
INSERT INTO contratos (
    id_personal, empleador_razon_social, empleador_ruc, empleador_domicilio, empleador_sede,
    empleador_departamento, empleador_provincia, empleador_distrito,
    empleado_dni, empleado_nombres, genero, empleado_domicilio,
    empleado_departamento, empleado_provincia, empleado_distrito, empleado_cargo,
    empleado_cantidad_ventas, fecha_inicio, fecha_fin, categoria_contrato,
    sueldo, numero_letras, importe, importe_letras, fecha_firma, email, empleado_telefono
) VALUES
(1, 'MIDAS SOLUTIONS CENTER E.I.R.L.', '20605357734', 'ALFREDO MENDIOLA # 6315', 'CHICLAYO',
 'LIMA', 'LIMA', 'LOS OLIVOS', '43728495', 'EBER FERNANDO VALLEJOS MANAYAY', 'MASCULINO',
 'CALLE VENEZUELA 240 U.VECINAL VICTOR R. HAYA DE LA TORRE', 'LAMBAYEQUE', 'FERREÑAFE', 'FERREÑAFE',
 'ASESOR', 6, '2024-01-15', '2024-12-31', 'COMERCIAL', 1130.00, 'MIL CIENTO TREINTA SOLES',
 200.00, 'DOSCIENTOS SOLES', '15 DE ENERO DE 2024', '<EMAIL>', '987654321'),

(2, 'MIDAS SOLUTIONS CENTER E.I.R.L.', '20605357734', 'ALFREDO MENDIOLA # 6315', 'CHICLAYO',
 'LIMA', 'LIMA', 'LOS OLIVOS', '87654321', 'MARIA ELENA RODRIGUEZ SILVA', 'FEMENINO',
 'AV. PRINCIPAL 123 URB. LAS FLORES', 'LIMA', 'LIMA', 'SAN MIGUEL',
 'GERENTE RRHH', NULL, '2023-03-01', '2025-02-28', 'GERENCIAL', 2500.00, 'DOS MIL QUINIENTOS SOLES',
 NULL, NULL, '01 DE MARZO DE 2023', '<EMAIL>', '987123456');

-- Insertar vacaciones de ejemplo
INSERT INTO vacaciones (
    id_personal, fecha_inicio, fecha_fin, dias_solicitados, dias_disponibles,
    estado, motivo, observaciones, fecha_solicitud, fecha_respuesta, aprobado_por
) VALUES
(1, '2024-12-15', '2024-12-29', 15, 30, 'APROBADO', 'Vacaciones de fin de año',
 'Viaje familiar programado', '2024-11-15', '2024-11-16 14:30:00', 'MARIA ELENA RODRIGUEZ SILVA'),

(1, '2024-07-01', '2024-07-15', 15, 30, 'APROBADO', 'Vacaciones de medio año',
 NULL, '2024-06-01', '2024-06-02 11:00:00', 'MARIA ELENA RODRIGUEZ SILVA'),

(2, '2024-08-15', '2024-08-19', 5, 30, 'APROBADO', 'Descanso personal',
 'Necesito tiempo para asuntos personales', '2024-07-20', '2024-07-21 09:15:00', 'Gerente General'),

(3, '2025-03-15', '2025-03-22', 8, 30, 'PENDIENTE', 'Vacaciones familiares',
 'Celebración de aniversario de bodas', '2025-01-15', NULL, NULL);

-- Insertar usuarios de ejemplo
INSERT INTO usuarios (
    id_personal, username, email, password_hash, nombres, apellidos, rol
) VALUES
(2, 'maria.rodriguez', '<EMAIL>',
 '$2y$10$example_hash_here', 'MARIA ELENA', 'RODRIGUEZ SILVA', 'RRHH'),

(NULL, 'admin', '<EMAIL>',
 '$2y$10$example_hash_here', 'ADMINISTRADOR', 'SISTEMA', 'ADMIN'),

(1, 'eber.vallejos', '<EMAIL>',
 '$2y$10$example_hash_here', 'EBER FERNANDO', 'VALLEJOS MANAYAY', 'EMPLEADO');

-- =====================================================
-- ÍNDICES ADICIONALES PARA OPTIMIZACIÓN
-- =====================================================

-- Índices compuestos para consultas frecuentes
CREATE INDEX idx_personal_empresa_estado ON personal(id_empresa, estado);
CREATE INDEX idx_contratos_personal_fechas ON contratos(id_personal, fecha_inicio, fecha_fin);
CREATE INDEX idx_vacaciones_personal_estado ON vacaciones(id_personal, estado);
CREATE INDEX idx_vacaciones_fechas ON vacaciones(fecha_inicio, fecha_fin);
CREATE INDEX idx_saldo_personal_anio ON saldo_vacaciones(id_personal, anio);

-- =====================================================
-- CONSULTAS ÚTILES DE EJEMPLO
-- =====================================================

/*
-- CONSULTAS FRECUENTES PARA EL SISTEMA:

-- 1. Ver todos los empleados activos con su empresa
SELECT * FROM vista_personal_completo WHERE estado_personal = 'ACTIVO';

-- 2. Ver contratos que vencen en los próximos 30 días
SELECT * FROM vista_contratos_activos WHERE estado_vigencia = 'POR_VENCER';

-- 3. Ver solicitudes de vacaciones pendientes
SELECT * FROM vista_vacaciones_completa WHERE estado = 'PENDIENTE';

-- 4. Ver resumen de vacaciones por empleado para el año actual
SELECT * FROM vista_resumen_vacaciones WHERE anio = YEAR(CURDATE());

-- 5. Buscar empleado por documento
SELECT * FROM vista_personal_completo WHERE documento = '43728495';

-- 6. Ver historial de cambios de un empleado
SELECT * FROM auditoria WHERE tabla = 'personal' AND id_registro = 1 ORDER BY fecha_hora DESC;

-- 7. Empleados por empresa
SELECT e.razon_social, COUNT(p.id_personal) as total_empleados
FROM empresas e
LEFT JOIN personal p ON e.id_empresa = p.id_empresa AND p.estado = 'ACTIVO'
GROUP BY e.id_empresa, e.razon_social;

-- 8. Contratos por categoría
SELECT categoria_contrato, COUNT(*) as total, AVG(sueldo) as sueldo_promedio
FROM contratos
WHERE estado = 'A'
GROUP BY categoria_contrato;

-- 9. Días de vacaciones utilizados por mes
SELECT
    YEAR(fecha_inicio) as anio,
    MONTH(fecha_inicio) as mes,
    SUM(dias_solicitados) as total_dias
FROM vacaciones
WHERE estado = 'APROBADO'
GROUP BY YEAR(fecha_inicio), MONTH(fecha_inicio)
ORDER BY anio DESC, mes DESC;

-- 10. Empleados con más días de vacaciones disponibles
SELECT
    vpc.nombre_completo,
    sv.dias_disponibles,
    sv.anio
FROM saldo_vacaciones sv
INNER JOIN vista_personal_completo vpc ON sv.id_personal = vpc.id_personal
WHERE sv.anio = YEAR(CURDATE())
ORDER BY sv.dias_disponibles DESC;
*/

-- =====================================================
-- SCRIPT COMPLETADO
-- =====================================================

/*
INSTRUCCIONES DE USO:

1. Ejecuta este script completo en tu servidor MySQL
2. Verifica que todas las tablas se hayan creado correctamente
3. Los datos de ejemplo te permitirán probar inmediatamente el sistema
4. Modifica los datos de ejemplo según tus necesidades
5. Configura las claves foráneas según tu estructura existente
6. Ajusta los procedimientos almacenados según tus reglas de negocio

CARACTERÍSTICAS INCLUIDAS:

✅ Tablas completas: empresas, personal, contratos, vacaciones, usuarios, auditoría
✅ Vistas optimizadas para consultas frecuentes
✅ Procedimientos almacenados para operaciones comunes
✅ Triggers para auditoría y validaciones
✅ Datos de ejemplo listos para pruebas
✅ Índices optimizados para rendimiento
✅ Consultas de ejemplo documentadas
✅ Estructura escalable y mantenible

PRÓXIMOS PASOS:

1. Crear API REST para conectar con Angular
2. Implementar autenticación y autorización
3. Agregar más validaciones de negocio
4. Configurar backups automáticos
5. Implementar logs de aplicación
6. Agregar más reportes y estadísticas

¡El sistema está listo para usar!
*/
