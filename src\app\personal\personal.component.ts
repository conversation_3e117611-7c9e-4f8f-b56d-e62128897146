import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Personal, PersonalService } from '../servicios/personal.service';
import { HttpClientModule } from '@angular/common/http';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { map, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Router, RouterLink } from '@angular/router';

@Component({
  selector: 'app-personal',
  standalone: true,
  imports: [CommonModule, FormsModule, HttpClientModule, RouterLink],
  templateUrl: './personal.component.html',
  styleUrls: ['./personal.component.scss'],
  providers: [PersonalService]
})
export class PersonalComponent implements OnInit {
  isLoading: boolean = false;

  private _personal = new BehaviorSubject<Personal[]>([]);
  personal$ = this._personal.asObservable();

  filterText: string = '';
  private _filterText = new BehaviorSubject<string>('');

  currentPage: number = 1;
  private _currentPage = new BehaviorSubject<number>(1);

  itemsPerPage: number = 10;
  private _itemsPerPage = new BehaviorSubject<number>(10);

  pageSizeOptions: number[] = [10, 20, 30];

  displayedPersonal$: Observable<Personal[]>;
  totalPages$: Observable<number>;

  constructor(private personalService: PersonalService, private router: Router) {
    this.displayedPersonal$ = combineLatest([
      this.personal$,
      this._filterText.pipe(debounceTime(300), distinctUntilChanged()),
      this._currentPage,
      this._itemsPerPage
    ]).pipe(
      map(([personalList, filterText, currentPage, itemsPerPage]) => {
        const filtered = personalList.filter(personal => {
          const tipoDocumento = personal.tipo_documento ?? '';
          const documento = personal.documento ?? '';
          const nombresCompletos = personal.apellidos_nombres_completos ?? '';
          const fechaNacimiento = personal.fecha_nacimiento ?? '';
          const edad = personal.edad ? personal.edad.toString() : '';
          const razonSocial = personal.razon_social ?? '';
          const cargo = personal.cargo ?? '';
          const sede = personal.sede ?? '';
          const search = filterText.toLowerCase();
          return (
            tipoDocumento.toLowerCase().includes(search) ||
            documento.toLowerCase().includes(search) ||
            nombresCompletos.toLowerCase().includes(search) ||
            fechaNacimiento.toLowerCase().includes(search) ||
            edad.includes(search) ||
            razonSocial.toLowerCase().includes(search) ||
            cargo.toLowerCase().includes(search) ||
            sede.toLowerCase().includes(search)
          );
        });

        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filtered.slice(startIndex, endIndex);
      })
    );

    this.totalPages$ = combineLatest([
      this.personal$,
      this._filterText.pipe(debounceTime(300), distinctUntilChanged()),
      this._itemsPerPage
    ]).pipe(
      map(([personalList, filterText, itemsPerPage]) => {
        const filtered = personalList.filter(personal => {
          const tipoDocumento = personal.tipo_documento ?? '';
          const documento = personal.documento ?? '';
          const nombresCompletos = personal.apellidos_nombres_completos ?? '';
          const fechaNacimiento = personal.fecha_nacimiento ?? '';
          const edad = personal.edad ? personal.edad.toString() : '';
          const razonSocial = personal.razon_social ?? '';
          const cargo = personal.cargo ?? '';
          const sede = personal.sede ?? '';
          const search = filterText.toLowerCase();
          return (
            tipoDocumento.toLowerCase().includes(search) ||
            documento.toLowerCase().includes(search) ||
            nombresCompletos.toLowerCase().includes(search) ||
            fechaNacimiento.toLowerCase().includes(search) ||
            edad.includes(search) ||
            razonSocial.toLowerCase().includes(search) ||
            cargo.toLowerCase().includes(search) ||
            sede.toLowerCase().includes(search)
          );
        });
        return Math.ceil(filtered.length / itemsPerPage);
      })
    );
  }

  ngOnInit(): void {
    this.loadPersonal();
  }

  loadPersonal(): void {
    this.isLoading = true;
    this.personalService.getPersonalGeneral().subscribe({
      next: (data) => {
        this._personal.next(data);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error al cargar personal:', error);
        this.isLoading = false;
      }
    });
  }

  onFilterTextChange(text: string): void {
    this.filterText = text;
    this._filterText.next(text);
    this._currentPage.next(1);
  }

  goToPage(page: number): void {
    this.currentPage = page;
    this._currentPage.next(page);
  }

  onPageSizeChange(event: Event): void {
    const newSize = +(event.target as HTMLSelectElement).value;
    this.itemsPerPage = newSize;
    this._itemsPerPage.next(newSize);
    this.currentPage = 1;
    this._currentPage.next(1);
  }

  getPageNumbers(totalPages: number): number[] {
    return Array(totalPages).fill(0).map((_, i) => i + 1);
  }
}
