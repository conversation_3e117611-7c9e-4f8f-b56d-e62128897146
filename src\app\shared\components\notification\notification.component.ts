import { Component, OnD<PERSON>roy, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { Notification, NotificationService } from '../../../servicios/notification.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss'],
  animations: [
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'translateY(-20px)'
      })),
      transition('void <=> *', animate('300ms ease-in-out')),
    ]),
  ],
})
export class NotificationComponent implements OnInit, OnDestroy {
  notifications: Notification[] = [];
  private subscription: Subscription | undefined;

  constructor(
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.subscription = this.notificationService.notification$.subscribe(notification => {
      console.log('Nueva notificación recibida:', notification);
      this.notifications.push(notification);

      // Forzar detección de cambios para mostrar inmediatamente
      this.cdr.detectChanges();

      if (notification.duration) {
        setTimeout(() => this.close(notification), notification.duration);
      }
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  close(notification: Notification): void {
    const index = this.notifications.indexOf(notification);
    if (index > -1) {
      this.notifications.splice(index, 1);
      // Forzar detección de cambios al cerrar
      this.cdr.detectChanges();
    }
  }

  getIconClass(type: 'success' | 'error' | 'info'): string {
    switch (type) {
      case 'success':
        return 'icon-success'; // Replace with actual icon class e.g. from FontAwesome
      case 'error':
        return 'icon-error';
      case 'info':
        return 'icon-info';
    }
  }
}
