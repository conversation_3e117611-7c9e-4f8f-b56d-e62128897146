import { Component, Input, OnChanges, OnInit, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotificationService } from '../../../servicios/notification.service';
import { VacacionesService } from '../../../services/vacaciones.service';

export interface Vacation {
  id_vacacion?: number;
  id_personal: number;
  fecha_inicio: string;
  fecha_fin: string;
  dias_solicitados: number;
  dias_asignados: number;
  dias_disponibles: number;
  dias_utilizados: number;
  dias_pendientes: number;
  estado: 'APROBADO';
  motivo?: string;
  observaciones?: string;
  fecha_solicitud: string;
  aprobado_por?: string;
  created_at?: string;
  updated_at?: string;
}

@Component({
  selector: 'app-personal-vacations',
  templateUrl: './personal-vacations.component.html',
  styleUrls: ['./personal-vacations.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class PersonalVacationsComponent implements OnInit, OnChanges {
  @Input() personalId: string | null = null;
  @Input() personalData: any = null;

  // Propiedades para gestión de vacaciones gozadas
  vacaciones: Vacation[] = [];
  isLoading: boolean = false;
  isSaving: boolean = false;

  // Propiedades para gestión de vacaciones compradas
  vacacionesCompradas: any[] = [];
  isLoadingCompradas: boolean = false;

  // Loading general para toda la página
  isLoadingGeneral: boolean = false;

  // Propiedades para nueva solicitud
  tipoVacaciones: 'GOZADAS' | 'COMPRADAS' = 'GOZADAS';
  diasComprados: number = 0;
  precioPorDia: number = 0;
  totalCompra: number = 0;
  anioSeleccionado: string = '2025';

  nuevaSolicitud: Vacation = {
    id_personal: 0,
    fecha_inicio: '',
    fecha_fin: '',
    dias_solicitados: 0,
    dias_asignados: 15,
    dias_disponibles: 15,
    dias_utilizados: 0,
    dias_pendientes: 15,
    estado: 'APROBADO',
    motivo: '',
    observaciones: '',
    fecha_solicitud: new Date().toISOString().split('T')[0]
  };

  // Propiedades para paginación - vacaciones gozadas
  currentPage: number = 1;
  itemsPerPage: number = 5;
  totalPages: number = 1;
  displayedVacaciones: Vacation[] = [];

  // Propiedades para paginación - vacaciones compradas
  currentPageCompradas: number = 1;
  itemsPerPageCompradas: number = 5;
  totalPagesCompradas: number = 1;
  displayedVacacionesCompradas: any[] = [];

  // Propiedades para filtros - vacaciones gozadas
  filtroEstado: string = '';
  filtroAnio: string = '';

  // Propiedades para filtros - vacaciones compradas
  filtroEstadoCompradas: string = '';
  filtroAnioCompradas: string = '';

  // Propiedad para controlar si el empleado tiene derecho a vacaciones
  tieneDerechoVacaciones: boolean = true;

  constructor(
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService,
    private vacacionesService: VacacionesService
  ) {}

  // Método para calcular tiempo de servicio
  calcularTiempoServicio(fechaIncorporacion: string | null | undefined): void {
    if (!fechaIncorporacion || !this.personalData) {
      if (this.personalData) {
        this.personalData.tiempo_servicio = '';
      }
      this.tieneDerechoVacaciones = false;
      return;
    }

    const fechaInicio = new Date(fechaIncorporacion);
    const hoy = new Date();

    // Ajustar las fechas a medianoche para evitar problemas con las zonas horarias
    fechaInicio.setUTCHours(0, 0, 0, 0);
    hoy.setUTCHours(0, 0, 0, 0);

    if (fechaInicio > hoy) {
      this.personalData.tiempo_servicio = 'Fecha futura';
      this.tieneDerechoVacaciones = false;
      return;
    }

    let years = hoy.getFullYear() - fechaInicio.getFullYear();
    let months = hoy.getMonth() - fechaInicio.getMonth();
    let days = hoy.getDate() - fechaInicio.getDate();

    if (days < 0) {
      months--;
      // Obtener el último día del mes anterior a 'hoy'
      const ultimoDiaMesAnterior = new Date(hoy.getFullYear(), hoy.getMonth(), 0).getDate();
      days += ultimoDiaMesAnterior;
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    this.personalData.tiempo_servicio = `${years} años ${months} meses ${days} días`;

    // Validar si tiene derecho a vacaciones (al menos 1 año de servicio)
    this.tieneDerechoVacaciones = years >= 1;
  }

  ngOnInit(): void {
    this.loadAllVacacionesData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['personalId'] && this.personalId) {
      this.nuevaSolicitud.id_personal = parseInt(this.personalId);
      this.loadAllVacacionesData();
    }

    // Calcular tiempo de servicio cuando cambian los datos del personal
    if (changes['personalData'] && this.personalData?.fecha_incorporacion) {
      this.calcularTiempoServicio(this.personalData.fecha_incorporacion);
    }
  }

  // Método principal que carga todos los datos con una sola llamada al backend
  loadAllVacacionesData(): void {
    if (!this.personalId || this.personalId === 'new') {
      return;
    }

    // Activar loading general y específicos
    this.isLoadingGeneral = true;
    this.isLoading = true;
    this.isLoadingCompradas = true;

    const idPersonal = parseInt(this.personalId);
    const anioSeleccionado = parseInt(this.anioSeleccionado);

    console.log(`🔄 Cargando datos de vacaciones para personal ${idPersonal}, año ${anioSeleccionado}`);

    // UNA SOLA llamada al backend
    this.vacacionesService.listarVacacionesPorPersonalAnio(idPersonal, anioSeleccionado)
      .subscribe({
        next: (vacaciones) => {
          console.log(`✅ Datos recibidos: ${vacaciones.length} registros`);

          // 1. Procesar vacaciones GOZADAS
          this.procesarVacacionesGozadas(vacaciones);

          // 2. Procesar vacaciones COMPRADAS
          this.procesarVacacionesCompradas(vacaciones);

          // 3. Calcular badges basándose en todos los datos
          this.calcularBadgesDesdeVacaciones(vacaciones);

          // Desactivar todos los loadings
          this.isLoadingGeneral = false;
          this.isLoading = false;
          this.isLoadingCompradas = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('❌ Error al cargar datos de vacaciones:', error);
          this.notificationService.error(`Error al cargar vacaciones: ${error.message}`);

          // En caso de error, limpiar datos y desactivar loadings
          this.vacaciones = [];
          this.vacacionesCompradas = [];
          this.updateDisplayedVacaciones();
          this.updateDisplayedVacacionesCompradas();

          // Valores por defecto para badges
          this.nuevaSolicitud.dias_asignados = 15;
          this.nuevaSolicitud.dias_disponibles = 15;
          this.nuevaSolicitud.dias_utilizados = 0;
          this.nuevaSolicitud.dias_pendientes = 15;

          this.isLoadingGeneral = false;
          this.isLoading = false;
          this.isLoadingCompradas = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Procesar datos para vacaciones gozadas
  private procesarVacacionesGozadas(vacaciones: any[]): void {
    // Filtrar solo vacaciones GOZADAS
    const vacacionesGozadas = vacaciones.filter(v =>
      v.dias_comprados === null && v.precio_por_dia === null && v.total_compra === null
    );

    // Mapear datos del backend al formato del componente
    this.vacaciones = vacacionesGozadas.map(v => ({
      id_vacacion: v.id_vacaciones_personal,
      id_personal: v.id_personal,
      fecha_inicio: v.fecha_inicio,
      fecha_fin: v.fecha_fin,
      dias_solicitados: v.dias_solicitados,
      dias_asignados: v.dias_asignados,
      dias_disponibles: v.dias_disponibles,
      dias_utilizados: v.dias_utilizados,
      dias_pendientes: v.dias_pendientes,
      estado: v.estado || 'APROBADO',
      motivo: v.motivo || '',
      observaciones: v.observaciones || '',
      fecha_solicitud: v.fecha_solicitud || '',
      aprobado_por: v.aprobado_por || '',
      created_at: v.created_at || '',
      updated_at: v.updated_at || ''
    }));

    console.log(`📋 Vacaciones GOZADAS procesadas: ${this.vacaciones.length}`);
    this.updateDisplayedVacaciones();
  }

  // Procesar datos para vacaciones compradas
  private procesarVacacionesCompradas(vacaciones: any[]): void {
    // Filtrar solo vacaciones COMPRADAS
    const vacacionesCompradas = vacaciones.filter(v =>
      v.dias_comprados !== null && v.precio_por_dia !== null && v.total_compra !== null
    );

    // Mapear datos del backend al formato del componente
    this.vacacionesCompradas = vacacionesCompradas.map(v => ({
      id_vacacion_comprada: v.id_vacaciones_personal,
      id_personal: v.id_personal,
      dias_comprados: v.dias_comprados!,
      precio_por_dia: v.precio_por_dia!,
      total_compra: v.total_compra!,
      estado: v.estado || 'APROBADO',
      observaciones: v.observaciones || '',
      fecha_solicitud: v.fecha_solicitud || '',
      aprobado_por: v.aprobado_por || '',
      created_at: v.created_at || '',
      updated_at: v.updated_at || ''
    }));

    console.log(`💳 Vacaciones COMPRADAS procesadas: ${this.vacacionesCompradas.length}`);
    this.updateDisplayedVacacionesCompradas();
  }

  // Cargar vacaciones del personal (MÉTODO LEGACY - mantener por compatibilidad)
  loadVacaciones(): void {
    if (!this.personalId || this.personalId === 'new') {
      return;
    }

    this.isLoading = true;
    const idPersonal = parseInt(this.personalId);
    const anioSeleccionado = parseInt(this.anioSeleccionado);

    // Cargar vacaciones desde el backend
    this.vacacionesService.listarVacacionesPorPersonalAnio(idPersonal, anioSeleccionado)
      .subscribe({
        next: (vacaciones) => {
          // Filtrar solo vacaciones GOZADAS (dias_comprados, precio_por_dia, total_compra son null)
          const vacacionesGozadas = vacaciones.filter(v =>
            v.dias_comprados === null && v.precio_por_dia === null && v.total_compra === null
          );

          // Mapear datos del backend al formato del componente
          this.vacaciones = vacacionesGozadas.map(v => ({
            id_vacacion: v.id_vacaciones_personal,
            id_personal: v.id_personal,
            fecha_inicio: v.fecha_inicio,
            fecha_fin: v.fecha_fin,
            dias_solicitados: v.dias_solicitados,
            dias_asignados: v.dias_asignados,
            dias_disponibles: v.dias_disponibles,
            dias_utilizados: v.dias_utilizados,
            dias_pendientes: v.dias_pendientes,
            estado: v.estado || 'APROBADO',
            motivo: v.motivo || '',
            observaciones: v.observaciones || '',
            fecha_solicitud: v.fecha_solicitud || '',
            aprobado_por: v.aprobado_por || '',
            created_at: v.created_at || '',
            updated_at: v.updated_at || ''
          }));

          console.log(`Vacaciones GOZADAS cargadas: ${this.vacaciones.length} de ${vacaciones.length} total`);
          this.updateDisplayedVacaciones();
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error al cargar vacaciones:', error);
          this.notificationService.error(`Error al cargar vacaciones: ${error.message}`);
          this.vacaciones = []; // Array vacío en caso de error
          this.updateDisplayedVacaciones();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Cargar resumen de vacaciones para actualizar badges
  loadResumenVacaciones(): void {
    if (!this.personalId || this.personalId === 'new') {
      return;
    }

    const idPersonal = parseInt(this.personalId);
    const anioSeleccionado = parseInt(this.anioSeleccionado);

    // Cargar todas las vacaciones para calcular los badges
    this.vacacionesService.listarVacacionesPorPersonalAnio(idPersonal, anioSeleccionado)
      .subscribe({
        next: (vacaciones) => {
          this.calcularBadgesDesdeVacaciones(vacaciones);
        },
        error: (error) => {
          console.error('Error al obtener vacaciones para badges:', error);
          // En caso de error, usar valores por defecto
          this.nuevaSolicitud.dias_asignados = 15;
          this.nuevaSolicitud.dias_disponibles = 15;
          this.nuevaSolicitud.dias_utilizados = 0;
          this.nuevaSolicitud.dias_pendientes = 15;
          this.cdr.detectChanges();
        }
      });
  }

  // Calcular valores de badges basándose en los datos de vacaciones
  private calcularBadgesDesdeVacaciones(vacaciones: any[]): void {
    console.log('Calculando badges desde vacaciones:', vacaciones);

    if (vacaciones.length === 0) {
      // Si no hay datos, usar valores por defecto
      this.nuevaSolicitud.dias_asignados = 15;
      this.nuevaSolicitud.dias_disponibles = 15;
      this.nuevaSolicitud.dias_utilizados = 0;
      this.nuevaSolicitud.dias_pendientes = 15;
    } else {
      // Calcular días utilizados sumando todos los dias_solicitados
      const diasUtilizados = vacaciones.reduce((total, vacacion) => {
        return total + (vacacion.dias_solicitados || 0);
      }, 0);

      // Valores calculados
      const diasAsignados = 15; // Valor fijo
      const diasPendientes = diasAsignados - diasUtilizados;
      const diasDisponibles = diasPendientes; // Por ahora ocupar los pendientes

      // Actualizar badges
      this.nuevaSolicitud.dias_asignados = diasAsignados;
      this.nuevaSolicitud.dias_disponibles = diasDisponibles;
      this.nuevaSolicitud.dias_utilizados = diasUtilizados;
      this.nuevaSolicitud.dias_pendientes = diasPendientes;

      console.log('Badges calculados:', {
        asignados: diasAsignados,
        utilizados: diasUtilizados,
        pendientes: diasPendientes,
        disponibles: diasDisponibles
      });
    }

    this.cdr.detectChanges();
  }

  // Calcular días entre fechas
  calcularDias(): void {
    if (this.nuevaSolicitud.fecha_inicio && this.nuevaSolicitud.fecha_fin) {
      const inicio = new Date(this.nuevaSolicitud.fecha_inicio);
      const fin = new Date(this.nuevaSolicitud.fecha_fin);

      if (fin > inicio) {
        const diffTime = Math.abs(fin.getTime() - inicio.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir el día de inicio
        this.nuevaSolicitud.dias_solicitados = diffDays;

        // Validación visual en tiempo real
        if (diffDays > this.nuevaSolicitud.dias_pendientes) {
          console.warn(`⚠️ Días solicitados (${diffDays}) exceden días pendientes (${this.nuevaSolicitud.dias_pendientes})`);
        }
      } else {
        this.nuevaSolicitud.dias_solicitados = 0;
      }
    }
  }

  // Calcular total de compra de vacaciones
  calcularTotalCompra(): void {
    this.totalCompra = this.diasComprados * this.precioPorDia;

    // Validación visual en tiempo real para días comprados
    if (this.diasComprados > this.nuevaSolicitud.dias_pendientes) {
      console.warn(`⚠️ Días comprados (${this.diasComprados}) exceden días pendientes (${this.nuevaSolicitud.dias_pendientes})`);
    }
  }

  // Cambio de año seleccionado
  onAnioChange(): void {
    console.log('Año seleccionado:', this.anioSeleccionado);

    // Cargar todos los datos de vacaciones para el año seleccionado
    this.loadAllVacacionesData();

    this.notificationService.info(`Mostrando datos de vacaciones para el año ${this.anioSeleccionado}`);
  }

  // Validar fechas
  validarFechas(): boolean {
    const hoy = new Date();
    const inicio = new Date(this.nuevaSolicitud.fecha_inicio);
    const fin = new Date(this.nuevaSolicitud.fecha_fin);

    if (inicio < hoy) {
      this.notificationService.error('La fecha de inicio no puede ser anterior a hoy');
      return false;
    }

    if (fin <= inicio) {
      this.notificationService.error('La fecha de fin debe ser posterior a la fecha de inicio');
      return false;
    }

    if (this.nuevaSolicitud.dias_solicitados > this.nuevaSolicitud.dias_disponibles) {
      this.notificationService.error('No tienes suficientes días disponibles');
      return false;
    }

    return true;
  }

  // Guardar nueva solicitud
  guardarSolicitud(): void {
    if (!this.personalId || this.personalId === 'new') {
      this.notificationService.error('ID de personal no válido');
      return;
    }

    // Validar según el tipo de vacación
    if (this.tipoVacaciones === 'GOZADAS') {
      if (!this.validarFechas()) {
        return;
      }
      if (!this.nuevaSolicitud.motivo?.trim()) {
        this.notificationService.error('El motivo es requerido');
        return;
      }

      // Validar que los días solicitados no excedan los días pendientes
      if (this.nuevaSolicitud.dias_solicitados > this.nuevaSolicitud.dias_pendientes) {
        this.notificationService.error(
          `No puede solicitar ${this.nuevaSolicitud.dias_solicitados} días. ` +
          `Solo tiene ${this.nuevaSolicitud.dias_pendientes} días pendientes disponibles.`
        );
        return;
      }

    } else if (this.tipoVacaciones === 'COMPRADAS') {
      if (this.diasComprados <= 0) {
        this.notificationService.error('Debe especificar días comprados');
        return;
      }
      if (this.precioPorDia <= 0) {
        this.notificationService.error('Debe especificar precio por día');
        return;
      }

      // Validar que los días comprados no excedan los días pendientes
      if (this.diasComprados > this.nuevaSolicitud.dias_pendientes) {
        this.notificationService.error(
          `No puede comprar ${this.diasComprados} días. ` +
          `Solo tiene ${this.nuevaSolicitud.dias_pendientes} días pendientes disponibles.`
        );
        return;
      }
    }

    this.isSaving = true;

    const idPersonal = parseInt(this.personalId);
    const anioSeleccionado = parseInt(this.anioSeleccionado);

    // Debug: Verificar valores de badges antes de enviar
    console.log('Valores de badges antes de enviar:', {
      dias_asignados: this.nuevaSolicitud.dias_asignados,
      dias_disponibles: this.nuevaSolicitud.dias_disponibles,
      dias_utilizados: this.nuevaSolicitud.dias_utilizados,
      dias_pendientes: this.nuevaSolicitud.dias_pendientes,
      anio_seleccionado: anioSeleccionado,
      tipo_vacaciones: this.tipoVacaciones
    });

    if (this.tipoVacaciones === 'GOZADAS') {
      // Crear vacación gozada
      this.vacacionesService.crearVacacionGozada(
        idPersonal,
        this.nuevaSolicitud.fecha_inicio,
        this.nuevaSolicitud.fecha_fin,
        this.nuevaSolicitud.dias_solicitados,
        this.nuevaSolicitud.motivo || '',
        this.nuevaSolicitud.observaciones,
        anioSeleccionado,
        this.nuevaSolicitud.dias_asignados,
        this.nuevaSolicitud.dias_disponibles,
        this.nuevaSolicitud.dias_utilizados,
        this.nuevaSolicitud.dias_pendientes
      ).subscribe({
        next: () => {
          // Recargar todos los datos con una sola llamada
          this.loadAllVacacionesData();

          // Resetear formulario y mostrar éxito
          this.resetForm();
          this.isSaving = false;
          this.notificationService.success('Solicitud de vacaciones gozadas enviada correctamente');
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.isSaving = false;
          this.notificationService.error(`Error al crear vacación: ${error.message}`);
          console.error('Error al crear vacación gozada:', error);
        }
      });
    } else {
      // Crear vacación comprada
      this.vacacionesService.crearVacacionComprada(
        idPersonal,
        this.diasComprados,
        this.precioPorDia,
        this.nuevaSolicitud.observaciones,
        anioSeleccionado,
        this.nuevaSolicitud.dias_asignados,
        this.nuevaSolicitud.dias_disponibles,
        this.nuevaSolicitud.dias_utilizados,
        this.nuevaSolicitud.dias_pendientes
      ).subscribe({
        next: () => {
          // Recargar todos los datos con una sola llamada
          this.loadAllVacacionesData();

          // Resetear formulario y mostrar éxito
          this.resetForm();
          this.isSaving = false;
          this.notificationService.success('Solicitud de vacaciones compradas enviada correctamente');
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.isSaving = false;
          this.notificationService.error(`Error al crear vacación comprada: ${error.message}`);
          console.error('Error al crear vacación comprada:', error);
        }
      });
    }
  }

  // Resetear formulario
  resetForm(): void {
    this.tipoVacaciones = 'GOZADAS';
    this.diasComprados = 0;
    this.precioPorDia = 0;
    this.totalCompra = 0;
    // No resetear el año seleccionado para mantener la selección del usuario

    this.nuevaSolicitud = {
      id_personal: parseInt(this.personalId || '0'),
      fecha_inicio: '',
      fecha_fin: '',
      dias_solicitados: 0,
      dias_asignados: 15,
      dias_utilizados: 15,
      dias_pendientes: 15,
      dias_disponibles: 15,
      estado: 'APROBADO',
      motivo: '',
      observaciones: '',
      fecha_solicitud: new Date().toISOString().split('T')[0]
    };
  }

  // Funciones de paginación
  updateDisplayedVacaciones(): void {
    let filteredVacaciones = [...this.vacaciones];

    // Aplicar filtros
    if (this.filtroEstado) {
      filteredVacaciones = filteredVacaciones.filter(v => v.estado === this.filtroEstado);
    }

    if (this.filtroAnio) {
      filteredVacaciones = filteredVacaciones.filter(v => 
        v.fecha_inicio.startsWith(this.filtroAnio)
      );
    }

    this.totalPages = Math.ceil(filteredVacaciones.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.displayedVacaciones = filteredVacaciones.slice(startIndex, endIndex);
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updateDisplayedVacaciones();
    }
  }

  onPageSizeChange(newSize: number): void {
    this.itemsPerPage = newSize;
    this.currentPage = 1;
    this.updateDisplayedVacaciones();
  }

  getPageNumbers(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  // Formatear fecha
  formatearFecha(fecha: string): string {
    if (!fecha) return '';
    try {
      const [year, month, day] = fecha.split('-');
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.toLocaleDateString('es-PE', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return fecha;
    }
  }

  // Obtener clase CSS según estado
  getEstadoClass(estado: string): string {
    switch (estado) {
      case 'APROBADO':
        return 'bg-green-100 text-green-800';
      case 'RECHAZADO':
        return 'bg-red-100 text-red-800';
      case 'PENDIENTE':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Aplicar filtros
  aplicarFiltros(): void {
    this.currentPage = 1;
    this.updateDisplayedVacaciones();
  }

  // Limpiar filtros
  limpiarFiltros(): void {
    this.filtroEstado = '';
    this.filtroAnio = '';
    this.currentPage = 1;
    this.updateDisplayedVacaciones();
  }

  // =====================================================
  // MÉTODOS PARA VACACIONES COMPRADAS
  // =====================================================

  // Cargar vacaciones compradas del personal
  loadVacacionesCompradas(): void {
    if (!this.personalId || this.personalId === 'new') {
      return;
    }

    this.isLoadingCompradas = true;
    const idPersonal = parseInt(this.personalId);
    const anioSeleccionado = parseInt(this.anioSeleccionado);

    // Cargar vacaciones desde el backend (mismo endpoint)
    this.vacacionesService.listarVacacionesPorPersonalAnio(idPersonal, anioSeleccionado)
      .subscribe({
        next: (vacaciones) => {
          // Filtrar solo vacaciones COMPRADAS (dias_comprados, precio_por_dia, total_compra NO son null)
          const vacacionesCompradas = vacaciones.filter(v =>
            v.dias_comprados !== null && v.precio_por_dia !== null && v.total_compra !== null
          );

          // Mapear datos del backend al formato del componente
          this.vacacionesCompradas = vacacionesCompradas.map(v => ({
            id_vacacion_comprada: v.id_vacaciones_personal,
            id_personal: v.id_personal,
            dias_comprados: v.dias_comprados!,
            precio_por_dia: v.precio_por_dia!,
            total_compra: v.total_compra!,
            estado: v.estado || 'APROBADO',
            observaciones: v.observaciones || '',
            fecha_solicitud: v.fecha_solicitud || '',
            aprobado_por: v.aprobado_por || '',
            created_at: v.created_at || '',
            updated_at: v.updated_at || ''
          }));

          console.log(`Vacaciones COMPRADAS cargadas: ${this.vacacionesCompradas.length} de ${vacaciones.length} total`);
          this.updateDisplayedVacacionesCompradas();
          this.isLoadingCompradas = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error al cargar vacaciones compradas:', error);
          this.notificationService.error(`Error al cargar vacaciones compradas: ${error.message}`);
          this.vacacionesCompradas = []; // Array vacío en caso de error
          this.updateDisplayedVacacionesCompradas();
          this.isLoadingCompradas = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Funciones de paginación para vacaciones compradas
  updateDisplayedVacacionesCompradas(): void {
    let filteredVacaciones = [...this.vacacionesCompradas];

    // Aplicar filtros
    if (this.filtroEstadoCompradas) {
      filteredVacaciones = filteredVacaciones.filter(v => v.estado === this.filtroEstadoCompradas);
    }

    if (this.filtroAnioCompradas) {
      filteredVacaciones = filteredVacaciones.filter(v =>
        v.fecha_solicitud.startsWith(this.filtroAnioCompradas)
      );
    }

    this.totalPagesCompradas = Math.ceil(filteredVacaciones.length / this.itemsPerPageCompradas);
    const startIndex = (this.currentPageCompradas - 1) * this.itemsPerPageCompradas;
    const endIndex = startIndex + this.itemsPerPageCompradas;
    this.displayedVacacionesCompradas = filteredVacaciones.slice(startIndex, endIndex);
  }

  goToPageCompradas(page: number): void {
    if (page >= 1 && page <= this.totalPagesCompradas) {
      this.currentPageCompradas = page;
      this.updateDisplayedVacacionesCompradas();
    }
  }

  onPageSizeChangeCompradas(newSize: number): void {
    this.itemsPerPageCompradas = newSize;
    this.currentPageCompradas = 1;
    this.updateDisplayedVacacionesCompradas();
  }

  getPageNumbersCompradas(): number[] {
    return Array.from({ length: this.totalPagesCompradas }, (_, i) => i + 1);
  }

  // Aplicar filtros para vacaciones compradas
  aplicarFiltrosCompradas(): void {
    this.currentPageCompradas = 1;
    this.updateDisplayedVacacionesCompradas();
  }

  // Limpiar filtros para vacaciones compradas
  limpiarFiltrosCompradas(): void {
    this.filtroEstadoCompradas = '';
    this.filtroAnioCompradas = '';
    this.currentPageCompradas = 1;
    this.updateDisplayedVacacionesCompradas();
  }
}
