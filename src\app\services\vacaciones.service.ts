import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Interfaces para las respuestas del backend
export interface VacacionBackend {
  id_vacaciones_personal: number;
  id_personal: number;
  anio_seleccionado: number;
  fecha_inicio: string;
  fecha_fin: string;
  dias_solicitados: number;
  dias_asignados: number;
  dias_disponibles: number;
  dias_utilizados: number;
  dias_pendientes: number;
  tipo_vacaciones: 'GOZADAS' | 'COMPRADAS';
  estado?: 'APROBADO' | null;
  motivo?: string;
  observaciones?: string;
  fecha_solicitud?: string;
  aprobado_por?: string;
  created_at?: string;
  updated_at?: string;
  // Campos para vacaciones compradas
  dias_comprados?: number | null;
  precio_por_dia?: number | null;
  total_compra?: number | null;
  // Campos adicionales del backend
  apellidos_nombres_completos?: string;
  personal_documento?: string;
}

export interface ResumenVacaciones {
  id_personal: number;
  anio: number;
  dias_asignados: number;
  dias_disponibles: number;
  dias_utilizados: number;
  dias_pendientes: number;
  total_solicitudes: number;
  solicitudes_aprobadas: number;
  solicitudes_pendientes: number;
  solicitudes_rechazadas: number;
}

// Interfaces para el dashboard de vacaciones
export interface PersonalVacacion {
  id_personal: number;
  tipo_documento: string;
  documento: string;
  apellidos_nombres_completos: string;
  fecha_incorporacion: string;
  sede: string;
  tiempo_servicio: string;
  dias_asignados: number;
  dias_utilizados: number;
  dias_disponibles: number;
  vacaciones_de_ley: 'SI CORRESPONDE' | 'NO CORRESPONDE';
}

export interface DashboardVacaciones {
  anio_consultado: number;
  total_personal: number;
  total_corresponden: number;
  total_no_corresponden: number;
  totales_por_sede: { [sede: string]: number };
  personal: PersonalVacacion[];
}

// Interfaces para el dashboard de vacaciones
export interface PersonalVacacion {
  id_personal: number;
  tipo_documento: string;
  documento: string;
  apellidos_nombres_completos: string;
  fecha_incorporacion: string;
  sede: string;
  tiempo_servicio: string;
  dias_asignados: number;
  dias_utilizados: number;
  dias_disponibles: number;
  vacaciones_de_ley: 'SI CORRESPONDE' | 'NO CORRESPONDE';
}

export interface DashboardVacaciones {
  anio_consultado: number;
  total_personal: number;
  total_corresponden: number;
  total_no_corresponden: number;
  total_completas: number,
  totales_por_sede: { [sede: string]: number };
  personal: PersonalVacacion[];
}

export interface CrearVacacionRequest {
  id_personal: number;
  anio_seleccionado: number;
  fecha_inicio: string;
  fecha_fin: string;
  dias_solicitados: number;
  motivo: string;
  observaciones?: string;
  tipo_vacaciones: 'GOZADAS' | 'COMPRADAS';
  // Para vacaciones compradas
  dias_comprados?: number;
  precio_por_dia?: number;
  total_compra?: number;
  // Datos de los badges (spans)
  dias_asignados: number;
  dias_disponibles: number;
  dias_utilizados: number;
  dias_pendientes: number;
}

export interface ActualizarVacacionRequest {
  fecha_inicio?: string;
  fecha_fin?: string;
  dias_solicitados?: number;
  motivo?: string;
  observaciones?: string;
  estado?: 'APROBADO';
  aprobado_por?: string;
}

@Injectable({
  providedIn: 'root'
})
export class VacacionesService {
  private apiUrl = `${environment.apiUrl}/vacaciones`;

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) { }

  /**
   * Listar vacaciones por personal y año
   * GET /vacaciones/personal/{id_personal}/anio/{anio_seleccionado}/
   */
  listarVacacionesPorPersonalAnio(idPersonal: number, anioSeleccionado: number): Observable<VacacionBackend[]> {
    const url = `${this.apiUrl}/personal/${idPersonal}/anio/${anioSeleccionado}/`;
    return this.http.get<VacacionBackend[]>(url)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Obtener vacación por ID
   * GET /vacaciones/{id_vacaciones_personal}/
   */
  obtenerVacacionPorId(idVacacionesPersonal: number): Observable<VacacionBackend> {
    const url = `${this.apiUrl}/${idVacacionesPersonal}/`;
    return this.http.get<VacacionBackend>(url)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Crear nueva vacación
   * POST /vacaciones/crear/
   * Retorna la vacación creada junto con el resumen actualizado
   */
  crearVacacion(vacacion: CrearVacacionRequest, anioSeleccionado?: number): Observable<{vacacion: VacacionBackend, resumen: ResumenVacaciones}> {
    const url = `${this.apiUrl}/crear/`;
    const anio = anioSeleccionado || new Date().getFullYear();

    // Debug: Mostrar datos que se envían al backend
    console.log('Datos enviados al backend:', vacacion);

    return this.http.post<VacacionBackend>(url, vacacion, this.httpOptions)
      .pipe(
        // Después de crear la vacación, obtener el resumen actualizado
        switchMap((vacacionCreada: VacacionBackend) =>
          this.obtenerResumenVacaciones(vacacion.id_personal, anio).pipe(
            map(resumen => ({
              vacacion: vacacionCreada,
              resumen: resumen
            }))
          )
        ),
        catchError(this.handleError)
      );
  }

  /**
   * Actualizar vacación existente
   * PUT /vacaciones/actualizar/{id_vacaciones_personal}/
   */
  actualizarVacacion(idVacacionesPersonal: number, vacacion: ActualizarVacacionRequest): Observable<VacacionBackend> {
    const url = `${this.apiUrl}/actualizar/${idVacacionesPersonal}/`;
    return this.http.put<VacacionBackend>(url, vacacion, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Eliminar vacación
   * DELETE /vacaciones/eliminar/{id_vacaciones_personal}/
   */
  eliminarVacacion(idVacacionesPersonal: number): Observable<any> {
    const url = `${this.apiUrl}/eliminar/${idVacacionesPersonal}/`;
    return this.http.delete(url)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Obtener resumen de vacaciones por personal y año
   * GET /vacaciones/resumen/{id_personal}/anio/{anio_seleccionado}/
   */
  obtenerResumenVacaciones(idPersonal: number, anioSeleccionado: number): Observable<ResumenVacaciones> {
    const url = `${this.apiUrl}/resumen/${idPersonal}/anio/${anioSeleccionado}/`;
    return this.http.get<ResumenVacaciones>(url)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Obtener listado de personal con datos de vacaciones por año
   * GET /vacaciones/listado-personal/{anio}/
   */
  obtenerListadoPersonalVacaciones(anio: number): Observable<DashboardVacaciones> {
    const url = `${this.apiUrl}/listado-personal/${anio}/`;
    return this.http.get<DashboardVacaciones>(url)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Método auxiliar para crear vacación gozada
   */
  crearVacacionGozada(
    idPersonal: number,
    fechaInicio: string,
    fechaFin: string,
    diasSolicitados: number,
    motivo: string,
    observaciones?: string,
    anioSeleccionado?: number,
    diasAsignados?: number,
    diasDisponibles?: number,
    diasUtilizados?: number,
    diasPendientes?: number
  ): Observable<{vacacion: VacacionBackend, resumen: ResumenVacaciones}> {
    const anio = anioSeleccionado || new Date().getFullYear();
    const vacacion: CrearVacacionRequest = {
      id_personal: idPersonal,
      anio_seleccionado: anio,
      fecha_inicio: fechaInicio,
      fecha_fin: fechaFin,
      dias_solicitados: diasSolicitados,
      motivo: motivo,
      observaciones: observaciones,
      tipo_vacaciones: 'GOZADAS',
      dias_asignados: diasAsignados || 30,
      dias_disponibles: diasDisponibles || 15,
      dias_utilizados: diasUtilizados || 0,
      dias_pendientes: diasPendientes || 15
    };
    return this.crearVacacion(vacacion, anio);
  }

  /**
   * Método auxiliar para crear vacación comprada
   */
  crearVacacionComprada(
    idPersonal: number,
    diasComprados: number,
    precioPorDia: number,
    observaciones?: string,
    anioSeleccionado?: number,
    diasAsignados?: number,
    diasDisponibles?: number,
    diasUtilizados?: number,
    diasPendientes?: number
  ): Observable<{vacacion: VacacionBackend, resumen: ResumenVacaciones}> {
    const anio = anioSeleccionado || new Date().getFullYear();
    const vacacion: CrearVacacionRequest = {
      id_personal: idPersonal,
      anio_seleccionado: anio,
      fecha_inicio: '', // No aplica para compradas
      fecha_fin: '', // No aplica para compradas
      dias_solicitados: diasComprados,
      motivo: 'Compra de días de vacaciones',
      observaciones: observaciones,
      tipo_vacaciones: 'COMPRADAS',
      dias_comprados: diasComprados,
      precio_por_dia: precioPorDia,
      total_compra: diasComprados * precioPorDia,
      dias_asignados: diasAsignados || 30,
      dias_disponibles: diasDisponibles || 15,
      dias_utilizados: diasUtilizados || 0,
      dias_pendientes: diasPendientes || 15
    };
    return this.crearVacacion(vacacion, anio);
  }

  /**
   * Obtener estadísticas de vacaciones por año
   */
  obtenerEstadisticasVacaciones(idPersonal: number, anioSeleccionado: number): Observable<any> {
    return this.obtenerResumenVacaciones(idPersonal, anioSeleccionado).pipe(
      map(resumen => ({
        diasAsignados: resumen.dias_asignados,
        diasDisponibles: resumen.dias_disponibles,
        diasUtilizados: resumen.dias_utilizados,
        diasPendientes: resumen.dias_pendientes,
        porcentajeUtilizado: (resumen.dias_utilizados / resumen.dias_asignados) * 100,
        porcentajeDisponible: (resumen.dias_disponibles / resumen.dias_asignados) * 100
      }))
    );
  }

  /**
   * Validar si se pueden solicitar días de vacaciones
   */
  validarSolicitudVacaciones(idPersonal: number, anioSeleccionado: number, diasSolicitados: number): Observable<{valido: boolean, mensaje: string}> {
    return this.obtenerResumenVacaciones(idPersonal, anioSeleccionado).pipe(
      map(resumen => {
        if (diasSolicitados > resumen.dias_disponibles) {
          return {
            valido: false,
            mensaje: `No puede solicitar ${diasSolicitados} días. Solo tiene ${resumen.dias_disponibles} días disponibles.`
          };
        }
        if (diasSolicitados <= 0) {
          return {
            valido: false,
            mensaje: 'Debe solicitar al menos 1 día de vacaciones.'
          };
        }
        return {
          valido: true,
          mensaje: 'Solicitud válida.'
        };
      })
    );
  }



  /**
   * Formatear fecha para el backend (YYYY-MM-DD)
   */
  formatearFechaParaBackend(fecha: Date | string): string {
    if (typeof fecha === 'string') {
      return fecha;
    }
    return fecha.toISOString().split('T')[0];
  }

  /**
   * Manejo de errores HTTP
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'Ha ocurrido un error desconocido';

    if (error.error instanceof ErrorEvent) {
      // Error del lado del cliente
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Error del lado del servidor
      if (error.status === 0) {
        errorMessage = 'No se puede conectar con el servidor. Verifique su conexión.';
      } else if (error.status === 404) {
        errorMessage = 'Recurso no encontrado.';
      } else if (error.status === 400) {
        errorMessage = error.error?.message || 'Datos inválidos enviados al servidor.';
      } else if (error.status === 500) {
        errorMessage = 'Error interno del servidor.';
      } else {
        errorMessage = `Error ${error.status}: ${error.error?.message || error.message}`;
      }
    }

    console.error('Error en VacacionesService:', error);
    return throwError(() => new Error(errorMessage));
  }
}
