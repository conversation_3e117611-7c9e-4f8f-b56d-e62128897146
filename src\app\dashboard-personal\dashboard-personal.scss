// Estilos optimizados para el dashboard de personal
.dashboard-personal {
  .personal-table {
    th {
      @apply bg-gradient-to-r from-blue-100 to-indigo-100 px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider;
    }
    
    td {
      @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }
    
    tr {
      @apply hover:bg-blue-50 transition-colors duration-150;
      
      &:nth-child(even) {
        @apply bg-blue-50;
      }
    }
  }
  
  .employee-avatar {
    @apply h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    
    &.vigente { @apply bg-green-100 text-green-800 border border-green-200; }
    &.vencido { @apply bg-red-100 text-red-800 border border-red-200; }
    &.sin-contrato { @apply bg-gray-100 text-gray-800 border border-gray-200; }
  }
  
  .contract-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    
    &.con-archivo { @apply bg-green-100 text-green-800 border border-green-200; }
    &.sin-archivo { @apply bg-orange-100 text-orange-800 border border-orange-200; }
  }
  
  .info-icon {
    @apply w-4 h-4 mr-2;
    
    &.cargo { @apply text-indigo-500; }
    &.sede { @apply text-green-500; }
    &.fecha-inicio { @apply text-green-500; }
    &.fecha-fin { @apply text-red-500; }
  }
  
  .table-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50;
  }
  
  .pagination {
    @apply bg-gray-50 px-6 py-3 border-t border-gray-200;
    
    button {
      @apply px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed;
      
      &.active { @apply bg-blue-500 text-white border-blue-500; }
    }
  }
  
  .filters {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-6;
    
    select {
      @apply border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500;
    }
  }
  
  .stats-card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-4;
  }
  
  .stats-icon {
    @apply w-8 h-8;
    
    &.total { @apply text-blue-600; }
    &.vigentes { @apply text-green-600; }
    &.vencidos { @apply text-red-600; }
    &.sin-contrato { @apply text-gray-600; }
    &.sin-archivo { @apply text-orange-600; }
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .fade-in { animation: fadeIn 0.3s ease-in-out; }
  
  @media (max-width: 640px) {
    .personal-table th, .personal-table td { @apply px-3 py-2; }
    .filters { @apply flex-col space-y-2; }
    .filters select { @apply w-full; }
  }
}
