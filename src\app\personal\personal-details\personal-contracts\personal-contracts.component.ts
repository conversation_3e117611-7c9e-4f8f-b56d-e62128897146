import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import jsPDF from 'jspdf';
import { NumeroALetras } from '../../../utils/numero-a-letras';
import { ContratoService, Contrato } from '../../../services/contrato.service';
import { NotificationService } from '../../../servicios/notification.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-personal-contracts',
  templateUrl: './personal-contracts.component.html',
  styleUrls: ['./personal-contracts.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class PersonalContractsComponent implements OnInit, OnChanges {

  importe: number | null = null;
  sueldoEnLetras: string = '';
  importeEnLetras: string = '';

  @Input() personalId: string | null = null;
  @Input() personalData: any;
  @ViewChild('contractPreview') contractPreview!: ElementRef;

  isExpanded = true;
  errorMessage: string = '';
  showError: boolean = false;

  // Propiedades para subir archivo
  showModalSubirArchivo: boolean = false;
  contratoSeleccionado: any = null;
  archivoSeleccionado: File | null = null;
  errorArchivo: string = '';
  isSubiendoArchivo: boolean = false;
  isDragOver: boolean = false;
  successMessage: string = '';
  showSuccess: boolean = false;

  // Función estática para obtener fecha actual de Chiclayo
  private static getCurrentDateChiclayoStatic(): string {
    const now = new Date();
    const peruTime = new Date(now.getTime() - (5 * 60 * 60 * 1000)); // UTC-5

    const year = peruTime.getFullYear();
    const month = String(peruTime.getMonth() + 1).padStart(2, '0');
    const day = String(peruTime.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
  contrato: any = {
    empleador_razon_social: '',
    empleador_ruc: '',
    empleador_domicilio: '',
    empleador_sede: '',
    empleador_departamento: '',
    empleador_provincia: '',
    empleador_distrito: '',
    empleado_nombres: '',
    empleado_dni: '',
    empleado_domicilio: '',
    empleado_departamento: '',
    empleado_provincia: '',
    empleado_distrito: '',
    empleado_cargo: '',
    empleado_genero: '',
    empleado_cantidad_ventas: 7, // Valor por defecto
    fecha_inicio: PersonalContractsComponent.getCurrentDateChiclayoStatic(),
    fecha_fin: '',
    sueldo: null,
    numero_letras: '',
    importe: '270', // Prestaciones alimentarias
    email: '',
    empleado_telefono: '',
    Chiclayo: 'Chiclayo',
    fecha_firma: new Date().toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    }).toUpperCase()

  };
  contratos: Contrato[] = [];
  contratoGenerado: SafeHtml = '';
  showModal = false;

  // Propiedades de paginación
  currentPage: number = 1;
  itemsPerPage: number = 5;
  totalPages: number = 1;
  displayedContratos: Contrato[] = [];

  contratoTemplate = `
<div style="font-family: Times, serif; font-size: 10pt; line-height: 1.3; color: black; max-width: 100%; margin: 0; padding: 0;">

  <!-- Encabezado -->
  <div style="text-align: center; margin-bottom: 15px;">
    <div style="text-align: center; text-transform: uppercase; font-weight: bold; font-size: 14pt; margin: 8px 0; color: #09205D;">{{empleador_razon_social}}</div>
    <div style="text-align: center; text-transform: uppercase; font-weight: bold; font-size: 14pt; margin: 8px 0; color: #09205D;">RUC: {{empleador_ruc}}</div>
  </div>

  <!-- Título principal -->
  <h1 style="text-align: center; text-transform: uppercase; font-weight: bold; font-size: 11pt; margin: 10px 0; color: black;">CONTRATO DE TRABAJO DE MICROEMPRESA POR NECESIDAD DE MERCADO</h1>

  <p style="text-align: justify; margin: 8px 0;">Conste por el presente documento que se suscribe por duplicado con igual tenor y valor, un contrato de trabajo sujeto a modalidad de necesidades de mercado que al ampar de los artículos 54º y 58º de la Ley de Productividad y Competitividad Laboral, aprobada por Decreto Supremo Nº 003-97-TR, y asimismo bajo los alcances del DS 013-2013_ produce. Y/o y D.S. No. 08-2008-TR; que celebran de una parte:</p>

  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 6px 0; text-align: justify; list-style: none;">• <strong>{{empleador_razon_social}}</strong> con RUC Nº <strong>{{empleador_ruc}}</strong>, y domicilio en <strong>{{empleador_domicilio}}</strong> DEL DISTRITO Y PROVINCIA DE PIURA, DEPARTAMENTO PIURA; representado por su Titular Gerente MARITZA MIRIAM RAMIREZ AVILA con DNI Nº 40341592, la misma que tiene número de registro en la REMYPE Nº 0001718610-2019; a quien en adelante se denominará <strong>EL EMPLEADOR</strong>; y, de otra parte.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">• <strong>{{tratamiento_genero}} {{empleado_nombres}}</strong>, DNI Nº <strong>{{empleado_dni}}</strong>, con domicilio en <strong>{{empleado_domicilio}}</strong> DISTRITO DE PIURA, PROVINCIA DE PIURA, DEPARTAMENTO DE PIURA, a quien en adelante se denominará <strong>EL TRABAJADOR</strong>; en los términos y condiciones siguientes:</li>
  </ul>

  <p style="margin: 8px 0;"><strong>LAS PARTES</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA UNO.</h2>
  <p style="margin: 6px 0; text-align: justify;">{{texto_clausula_uno}}</p>

  <p style="margin: 8px 0;"><strong>OBJETIVOS DEL CONTRATO</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA DOS.</h2>
  <p style="margin: 6px 0; text-align: justify;">Por el presente documento <strong>EL EMPLEADOR</strong> contrata bajo la modalidad ya indicada, los servicios de <strong>EL TRABAJADOR</strong> quien desempeñará el cargo de <strong>TELEOPERADOR</strong>, por su tiempo de experiencia en el rubro mayor o igual a un año cumpliendo objetivos, en relación con las causas objetivas señaladas en la CLÁUSULA anterior. <strong>EL TRABAJADOR</strong> se obliga a prestar sus servicios al EMPLEADOR para realizar las siguientes actividades:</p>
  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 6px 0; text-align: justify; list-style: none;">• Gestión de ventas telefónicas, bajo las directivas de sus jefes o instructores, comprometiéndose <strong>EL TRABAJADOR</strong> a realizar un mínimo de 10 ventas concretadas al mes. Solo en el primer mes de labores, <strong>EL TRABAJADOR</strong> tendrá la salvedad de cumplir con una cuota mensual mínima de <strong>07</strong> ventas concretadas.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">• Las labores que se le impartan por necesidades del servicio en ejercicio de las facultades de administración y dirección de la empresa, de conformidad con el artículo 9º del Texto Único Ordenado de la Ley de Productividad y Competitividad Laboral, aprobado por Decreto Supremo Nº 003-97-TR.</li>
  </ul>

  <p style="margin: 6px 0; text-align: justify;"><strong>EL EMPLEADOR</strong> está facultado a efectuar modificaciones razonables en función a la capacidad y aptitud de <strong>EL TRABAJADOR</strong> y a las necesidades y requerimientos de la misma, sin que dichas variaciones signifiquen menoscabo de categoría y/o remuneración.</p>

  <p style="margin: 6px 0; text-align: justify;">Queda entendido que la prestación de servicios deberá ser efectuada de manera personal, no pudiendo <strong>EL TRABAJADOR</strong> ser reemplazado ni ayudado por tercera persona.</p>

  <p style="margin: 8px 0;"><strong>DURACIÓN DEL CONTRATO</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA TRES.</h2>
  <p style="margin: 6px 0; text-align: justify;">El presente contrato empezará a regir del <strong>{{fecha_inicio}}</strong> y concluirá el <strong>{{fecha_fin}}</strong> salvo que entre ambas partes se pacte la renovación del mismo, de acuerdo a lo estipulado del artículo 10 y 75 de la Ley de Productividad y Competitividad Laboral (D.S. 003-97-TR).</p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA CUATRO.</h2>
  <p style="margin: 6px 0; text-align: justify;">De tal forma, al amparo del artículo 10 del Texto Único Ordenado de la Ley de Productividad y Competitividad Laboral, Decreto Legislativo 728 aprobado por Decreto Supremo 003-97 TR, se precisa que el <span style="text-decoration: underline;"><strong>período de prueba será de 03 meses</strong></span>.</p>

  <p style="margin: 6px 0; text-align: justify;">Queda entendido que <strong>EL EMPLEADOR</strong> no está obligado a dar aviso alguno adicional referente al término del presente contrato, operando su extinción en la fecha de su vencimiento, conforme a la presente CLÁUSULA, oportunidad en la cual se abonará al TRABAJADOR los beneficios sociales, que le pudieran corresponder de acuerdo a Ley.</p>

  <p style="margin: 8px 0;"><strong>JORNADA ORDINARIA</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA cinco.</h2>
  <p style="margin: 6px 0; text-align: justify;"><strong>EL TRABAJADOR</strong> observará el horario de trabajo siguiente:</p>
  <p style="margin: 6px 0; text-align: justify;">De lunes a viernes:</p>
  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 4px 0; text-align: justify; list-style: none;">• Ingreso 9:00 am</li>
    <li style="margin: 4px 0; text-align: justify; list-style: none;">• Salida 6:30 pm</li>
    <li style="margin: 4px 0; text-align: justify; list-style: none;">• Break 60 min</li>
  </ul>

  <p style="margin: 6px 0; text-align: justify;">Sábados:</p>
  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 4px 0; text-align: justify; list-style: none;">• Ingreso 9:00 am</li>
    <li style="margin: 4px 0; text-align: justify; list-style: none;">• Salida 2:30 pm</li>
  </ul>

  <p style="margin: 6px 0; text-align: justify;">En uso de sus facultades directrices, <strong>EL EMPLEADOR</strong> está facultado a efectuar modificaciones razonables en la jornada de trabajo de acuerdo a sus necesidades operativas respetando el máximo legal de 48 horas semanales, sin que dichas variaciones signifiquen menoscabo de categoría y/o remuneración.</p>

  <p style="margin: 8px 0;"><strong>OBLIGACIONES DEL EMPLEADOR Y TRABAJADOR</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA Seis: Remuneración</h2>
  <p style="margin: 6px 0; text-align: justify;"><strong>EL TRABAJADOR</strong> tendrá derecho a 15 días de vacaciones por cada año laborado. Conforme al Artículo 50 DS 013-2013 - Produce. Y/O y D.S. No. 08-2008-TR.</p>

  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 6px 0; text-align: justify; list-style: none;">a. De igual forma, <strong>EL EMPLEADOR</strong> se compromete a brindar las capacitaciones y medios suficientes para que <strong>EL TRABAJADOR</strong> pueda cumplir con su cuota mensual mínima de 10 ventas concretadas. Solo en el primer mes de labores, el TRABAJADOR tendrá la salvedad de cumplir con una cuota mensual mínima de {{empleado_cantidad_ventas}} ventas concretadas.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">b. Asimismo, <strong>EL TRABAJADOR</strong> deberá cumplir con las normas propias del Centro de Trabajo, cumplir con las obligaciones y prohibiciones señaladas en la Ley de Productividad y Competitividad Laboral, así como las contenidas en el Reglamento Interno de Trabajo de la empresa, cumplir las políticas de seguridad y salud en el trabajo y demás políticas que exija <strong>EL EMPLEADOR</strong>. Cualquier violación a dichas normas, el plan para vigilancia, prevención y control del COVID-19 en el trabajo y directivas acarreará el correspondiente accionar de la empresa dentro del marco legal.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">c. <strong>EL TRABAJADOR</strong> se compromete, igualmente, a mantener en secreto toda información que llegue a su conocimiento en relación a los negocios de <strong>EL EMPLEADOR</strong>, sus asociados y/o clientes. Esta obligación subsistirá aun después de terminada la relación laboral y su incumplimiento genera la correspondiente responsabilidad por daños y perjuicios, sin desmedro de la persecución penal por el delito previsto en el artículo 165 del Código Penal o los que correspondan.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">d. Dado que el <strong>TRABAJADOR y EMPLEADOR</strong> reconocen que, durante la vigencia del contrato de trabajo, existe riesgo de desviación o aprovechamiento de los contactos o conocimientos a los que el <strong>TRABAJADOR</strong> ha obtenido acceso gracias a la prestación de servicios con el EMPLEADOR, en adelante know how; en razón a ello, <strong>EL TRABAJADOR</strong> se compromete a no ser parte en otros</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">e. contratos laborales o de prestación de servicios de manera directa o indirecta con cliente de <strong>EL EMPLEADOR</strong>, pues ambas partes reconocen que ello implica una concurrencia desleal en contra de <strong>EL EMPLEADOR</strong>, siendo que, si falta a dicho compromiso, el <strong>TRABAJADOR</strong> se compromete al pago de una indemnización por daños y perjuicios correspondiente, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. El incumplimiento del mismo, igualmente configura falta grave de despido conforme al inciso a) del artículo 25 del DS N° 001-97-TR.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">f. <strong>EL TRABAJADOR</strong> se compromete a, durante la prestación del vínculo laboral o luego de concluida la relación laboral, a resguardar el deber de confidencialidad y en ese sentido a no utilizar ni divulgar la base de datos de la empresa, sistemas, métodos de mercado y estrategias, información confidencial de los clientes, etc, para beneficio propio o de terceros; siendo que si falta a dicho compromiso, el <strong>TRABAJADOR</strong> se compromete al pago de una indemnización por daños y perjuicios correspondiente, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. Esta infracción al deber de confidencialidad asimismo produciría la configuración de Tráfico Ilegal de Datos Personales, previsto en el artículo 154-A del Código Penal y que tiene una pena prevista de 02 a 05 años de pena privativa de libertad.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">g. <strong>EL TRABAJADOR</strong> se compromete a no iniciar como socio, director, gerente, propietario o cualquier otro símil, una empresa o negocio a nivel local, regional o nacional, que tenga el mismo giro que el <strong>EMPLEADOR</strong>, utilizando la información reservada o confidencial de la empresa, como base de datos, sistemas, métodos de mercado y estrategas, información confidencial de los clientes, etc; siendo que, de faltar a dicho compromiso el <strong>TRABAJADOR</strong> se compromete al pago de una indemnización por daños y perjuicios correspondiente o mayor a esta, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. Esta infracción al deber de confidencialidad asimismo produciría la configuración del delito de Administración Fraudulenta de Persona Jurídica, previsto y sancionado en el artículo 198 inciso 6 del Código Penal que contempla una sanción de uno a cuatro años de pena privativa de libertad.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">h. <strong>EL TRABAJADOR</strong> se compromete a:</li>
    <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Tener Responsabilidad y Proactividad en el desempeño de su trabajo.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Desarrollar sus actividades con Capacidad de trabajo en equipo.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Presentar Puntualidad en su asistencia.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Presentar Compromiso en sus labores.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Realizar su trabajo con Sentido de seguridad.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Presentar Disposición al trabajo.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• Conservar al mínimo las Relaciones interpersonales con sus compañeros por posible fuga de bases de datos e información concedida para fines laborales.</li>
      <li style="margin: 4px 0; text-align: justify; list-style: none;">• No sostener relaciones de pareja con sus superiores o personal administrativo de la empresa.</li>
    </ul>
  </ul>

  <p style="margin: 8px 0;"><strong>REMUNERACIÓN</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA Siete.</h2>
  <p style="margin: 6px 0; text-align: justify;"><strong>EL EMPLEADOR</strong> abonará al <strong>TRABAJADOR</strong> la cantidad de <strong>S/{{sueldo}}</strong> soles <strong>(Mil ciento treinta soles)</strong> como remuneración mensual, de la cual se deducirá las aportaciones y descuentos por tributos establecidos en la ley que le resulten aplicables.<br>De igual forma EL EMPLEADOR cancelará a favor de <strong>EL TRABAJADOR</strong> la suma de <strong>S/{{importe}}</strong> soles <strong>(Doscientos setenta soles)</strong> como concepto no remunerativo de prestaciones alimentarias otorgadas bajo la modalidad de suministro indirecto, precisando que la misma es condición de trabajo sujeta a la asistencia a las labores, toda vez que la empresa tiene horario laboral corrido, teniendo de intermedio el refrigerio entre las horas de prestación efectiva de labores.</p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA ocho.</h2>
  <p style="margin: 6px 0; text-align: justify;">Las ausencias injustificadas por parte de <strong>EL TRABAJADOR</strong> implican la pérdida de la remuneración proporcionalmente a la duración de dicha ausencia, sin perjuicio del ejercicio de las facultades disciplinarias propias de <strong>EL EMPLEADOR</strong> previstas en la legislación laboral y normas internas de la empresa.</p>

  <p style="margin: 8px 0;"><strong>RESOLUCIÓN DEL CONTRATO</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA nueve.</h2>
  <p style="margin: 6px 0; text-align: justify;">Queda entendido que <strong>EL EMPLEADOR</strong> no está obligado a dar aviso adicional referente al término del presente contrato, operando su extinción en la fecha de su vencimiento conforme la CLÁUSULA tercera, Sistema Normativo de Información Laboral, oportunidad en la cual se abonará al <strong>TRABAJADOR</strong> los beneficios sociales que le pudieran corresponder de acuerdo a ley. De igual forma, <strong>EL EMPLEADOR</strong> tiene la facultad de iniciar el respectivo procedimiento disciplinario e imponer las sanciones correspondientes, en tanto a las normas vigentes. A efectos de la aplicación del artículo 24°, 25° y Siguientes del DL 728 también se entenderán como faltas graves las siguientes:</p>

  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 6px 0; text-align: justify; list-style: none;">A. Sustraer mobiliario o producto de las instalaciones de <strong>EL EMPLEADOR</strong> sin autorización expresa de este.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">B. <strong>EL TRABAJADOR</strong> se compromete a mantener en secreto toda la información de cualquier índole, que llegue a su conocimiento en relación a los negocios de <strong>EL EMPLEADOR</strong>, sus socios y/o clientes. Esta obligación subsistirá aun después de terminada la relación laboral y su incumplimiento genera la correspondiente responsabilidad por daños y perjuicios, así como la responsabilidad penal por el delito previsto en el artículo 165 del Código Penal.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">C. Truncar de manera deliberada las ventas asignadas a su cargo.</li>
  </ul>

  <p style="margin: 8px 0;"><strong>LEGISLACION APLICABLE</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA DIEZ.</h2>
  <p style="margin: 6px 0; text-align: justify;">Este contrato queda sujeto a las disposiciones que contiene el Texto Único Ordenado del D.Leg. Nº 728 aprobado por D.S. Nº 003-97-TR - Ley de Productividad y Competitividad Laboral, y de manera específica los beneficios laborales están regulados por DS 013-2013_ Produce. Y/O y D.S. No. 08-2008-TR y demás normas legales que lo regulen o que sean dictadas durante la vigencia del contrato.</p>

  <p style="margin: 8px 0;"><strong>EXTINCIÓN LABORAL POR CASO FORTUITO Y FUERZA MAYOR</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA once.</h2>
  <p style="margin: 6px 0; text-align: justify;">En caso de que ocurra un acontecimiento imprevisible, irresistible e inevitable que haga imposible la continuación de las labores, y en especial la pandemia del Coronavirus y otras de similares características que puedan surgir, tanto <strong>EL EMPLEADOR</strong> como <strong>EL TRABAJADOR</strong> acuerdan que de darse ese hecho especificado se procederá a <strong>EXTINGUIR</strong> los efectos del contrato de trabajo, de conformidad con lo prescrito por los artículos 12 literal l) y 15 del Texto Único Ordenado (TUO) del Decreto Legislativo N° 728, Ley de Productividad y Competitividad Laboral.</p>

  <p style="margin: 8px 0;"><strong>NOTIFICACIÓN ELECTRÓNICA</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA doce.</h2>
  <p style="margin: 6px 0; text-align: justify;">EL EMPLEADOR y EL TRABAJADOR que suscriben la presente CLÁUSULA, otorgan su conformidad para que EL TRABAJADOR sea notificado a la cuenta de correo electrónico que se detalla a continuación, con las decisiones que EL EMPLEADOR pueda adoptar respecto a contenidos o</p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA trece.</h2>
  <p style="margin: 6px 0; text-align: justify;">procedimientos que estén relacionados con la ejecución del contrato de trabajo celebrado entre ambos. Se entienden que están incorporadas dentro de los alcances de la presente CLÁUSULA las comunicaciones disciplinarias, de amonestaciones, suspensiones o que tengan que ver incluso con el pre aviso de despido y el despido.</p>

  <p style="margin: 6px 0;">Correo Electrónico: {{email}}</p>

  <p style="margin: 6px 0; text-align: justify;">Las partes acuerdan que los efectos de las notificaciones a la dirección electrónica señalada surten efectos a partir del día siguiente de notificada la decisión o contenido. Si el día siguiente a la notificación electrónica fuese un día inhábil, se entenderá que la notificación se realizó el día hábil inmediato posterior a la notificación electrónica.<br><strong>EL TRABAJADOR</strong> acepta las condiciones y efectos de la notificación electrónica que por la presente CLÁUSULA se prevé.<br>En caso que Empleador notifique la misma decisión o procedimiento a través de la notificación electrónica y también a través de la notificación física (postal), se entiende que <strong>EL TRABAJADOR</strong> quedará notificado con el contenido, decisión o procedimiento a partir del día siguiente de la notificación física.<br><strong>El TRABAJADOR</strong> se obliga a informar al <strong>EMPLEADOR</strong> ya sea por medio escrito o digital, si cambie de dirección mail o correo electrónico, de lo contrario, se reputará por vigente la establecida en el presente contrato.<br>En adición se mandará una alerta vía WhatsApp respecto de la remisión de la notificación electrónica al correo, registrándose para ello, el número del trabajador {{empleado_telefono}}.</p>

  <p style="margin: 8px 0;"><strong>RECOMENDACIONES DE SEGURIDAD Y SALUD EN EL TRABAJO</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA Décimo catorce.</h2>
  <p style="margin: 6px 0; text-align: justify;"><strong>EL TRABAJADOR</strong> deberá seguir las siguientes recomendaciones en materia de seguridad y salud en el trabajo:</p>

  <ul style="margin: 8px 0 8px 20px; padding-left: 0;">
    <li style="margin: 6px 0; text-align: justify; list-style: none;">A. Cumplir con el reglamento interno de seguridad y salud en el trabajo dentro de las instalaciones de <strong>EL EMPLEADOR</strong>.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">B. Cumplir con los procedimientos de inducción en seguridad y salud en el trabajo antes del ingreso a las instalaciones de <strong>EL EMPLEADOR</strong>; Así mismo participar de las capacitaciones y entrenamientos en los procedimientos que tiene que ejecutar para el cumplimiento de sus funciones y responsabilidades.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">C. Informarse sobre la conformación del comité de seguridad y salud en el trabajo de su unidad, quienes son sus representantes y cuáles son sus funciones y responsabilidades.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">D. Utilizar el equipo de protección personal necesario para la protección de la integridad y salud durante la ejecución de trabajos bajo su responsabilidad.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">E. Participar del a capacitación y entrenamiento para su correcto uso.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">F. Participar activamente en la prevención de riesgos de seguridad y salud en el trabajo mediante el reporte de actos o condiciones que ponga en riesgo la vida y/o salud propia y/o la de los demás trabajadores; además de los incidentes, accidentes y casi-accidentes que hayan ocurrido durante la ejecución del trabajo.</li>
    <li style="margin: 6px 0; text-align: justify; list-style: none;">G. Queda claramente establecido que dichas recomendaciones no exoneran a EL TRABAJADOR del conocimiento y cumplimiento de las demás normas y disposiciones de seguridad y salud en el trabajo que rijan en el centro de trabajo de EL EMPLEADOR, sean estas aplicables a su actividad especificas o dirigidas a la generalidad de trabajadores, tales como el reglamento interno de seguridad salud en el trabajo, los procedimientos de trabajo, las ordenes de trabajo, las instrucciones y recomendaciones emitidas en las capacitaciones impartidas, y cualquier otra sobre la materia.</li>
  </ul>

  <p style="margin: 8px 0;"><strong>ENTREGA DE EQUIPOS</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA QUINCE.</h2>
  <p style="margin: 6px 0; text-align: justify;"><strong>EL EMPLEADOR</strong> proporcionará al <strong>TRABAJADOR</strong> los materiales y equipos necesarios para el adecuado desarrollo de sus actividades. <strong>EL TRABAJADOR</strong> será responsable de los equipos y bienes de trabajo asignados los mismos que deben sufrir el desgaste propio y natural provocado por el uso normal. <strong>EL TRABAJADOR</strong> será responsable por los daños, pérdidas, extravíos o robos de los equipos y/o bienes de trabajo que se le hayan asignado. <strong>EL TRABAJADOR</strong> autoriza expresamente al empleador a deducir de su remuneración de su liquidación de sus beneficios sociales en caso de extinción de la relación laboral el costo de la reparación o reposición de los equipos de trabajo.</p>

  <p style="margin: 8px 0;"><strong>DE LA PROPIEDAD INTELECTUAL Y USO DE IMAGEN PARA FINES COMERCIALES Y PUBLICITARIOS</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA DIECISÉIS.</h2>
  <p style="margin: 6px 0; text-align: justify;"><strong>EL TRABAJADOR</strong> acepta que todo producto, ya sea de naturaleza, física, digital o cualquiera sea su naturaleza, que se generen en el marco de sus labores según el presente contrato de trabajo, son de entera propiedad de <strong>EL EMPLEADOR</strong> pues se producen en el marco de la relación laboral. De igual forma, <strong>EL TRABAJADOR</strong> autoriza al uso de su imagen en toda producción audiovisual, fotográfica, de branding o cualquier otro tipo de publicidad, banners u otros similares, que produzca el <strong>EMPLEADOR</strong> donde figuren tanto él como otro trabajadores o directivos.</p>

  <p style="margin: 8px 0;"><strong>JURISDICCIÓN Y COMPETENCIA</strong></p>

  <h2 style="font-size: 11pt; font-weight: bold; text-transform: uppercase; margin: 12px 0 6px; text-align: left;">CLÁUSULA DIECISIETE.</h2>
  <p style="margin: 6px 0; text-align: justify;">Las partes contratantes renuncian expresamente al fuero judicial de sus domicilios y se someten a la jurisdicción de los jueces de la ciudad de {{Chiclayo}} para resolver cualquier controversia que el cumplimiento del presente contrato pudiera originar. Conformes con todas las CLÁUSULAs del presente contrato, firman las partes por duplicado, el día {{fecha_firma}}.</p>

  <!-- Bloque de firmas -->
  <div style="display: flex; justify-content: space-between; margin-top: 40px; margin-bottom: 20px;">
    <div style="text-align: center; width: 45%;">
      <div style="border-top: 1px solid black; margin-top: 30px; padding-top: 5px;"></div>
      <div style="font-weight: bold;">{{empleador_razon_social}}</div>
      <div style="font-size: 9pt;">EL EMPLEADOR</div>
    </div>
    <div style="text-align: center; width: 45%;">
      <div style="border-top: 1px solid black; margin-top: 30px; padding-top: 5px;"></div>
      <div style="font-weight: bold;">{{empleado_nombres}}</div>
      <div style="font-size: 9pt;">DNI: {{empleado_dni}} | EL TRABAJADOR</div>
    </div>
  </div>

</div>
  `;

  constructor(
    private sanitizer: DomSanitizer,
    private contratoService: ContratoService,
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService,
    private http: HttpClient
  ) { }

  ngOnInit(): void {
    console.log('Cargando contratos para el ID de personal:', this.personalId);
    this.populateEmpleadoData();
    this.updateSueldoEnLetras();
    this.updateImporteEnLetras();

    // Cargar contratos existentes con un pequeño delay para que se vea el loading
    setTimeout(() => {
      this.cargarContratos();
    }, 500);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['personalData'] && this.personalData) {
      this.populateEmpleadoData();
      this.updateSueldoEnLetras();
      this.updateImporteEnLetras();
    }
  }

  updateSueldoEnLetras(): void {
    const monto = Number(this.contrato.sueldo || 0);
    this.sueldoEnLetras = NumeroALetras(monto, {
      monedaPlural: 'SOLES',
      monedaSingular: 'SOL',
      mayus: true,
      normalizarUnMil: true
    });
    this.contrato.numero_letras = this.sueldoEnLetras;
  }

  updateImporteEnLetras(): void {
    const monto = Number(this.contrato.importe || 0);
    this.importeEnLetras = NumeroALetras(monto, {
      monedaPlural: 'SOLES',
      monedaSingular: 'SOL',
      mayus: true,
      normalizarUnMil: true
    });
  }

  populateEmpleadoData(): void {
    if (this.personalData) {
      this.contrato.empleador_ruc = this.personalData.ruc || '';
      this.contrato.empleador_razon_social = this.personalData.razon_social || '';
      this.contrato.empleador_sede = this.personalData.sede || '';
      if (this.personalData.sede === 'CHICLAYO') {
        this.contrato.empleador_domicilio = 'CALLE TORRES PAZ # 199';
        this.contrato.empleador_departamento = 'LAMBAYEQUE';
        this.contrato.empleador_provincia =  'CHICLAYO';
        this.contrato.empleador_distrito = 'CHICLAYO';
      } else  if (this.personalData.sede === 'PIURA') {
        this.contrato.empleador_domicilio = 'CALLE AREQUIPA # 499';
        this.contrato.empleador_departamento = 'PIURA';
        this.contrato.empleador_provincia =  'PIURA';
        this.contrato.empleador_distrito = 'PIURA';
      } else  if (this.personalData.sede === 'LOS OLIVOS - LIMA') {
        this.contrato.empleador_domicilio = 'ALFREDO MENDIOLA # 6315';
        this.contrato.empleador_departamento = 'LIMA';
        this.contrato.empleador_provincia =  'LIMA';
        this.contrato.empleador_distrito = 'LOS OLIVOS';
      } else  if (this.personalData.sede === 'SAN MIGUEL - LIMA') {
        this.contrato.empleador_domicilio = 'AV. ELMER FAUCETT # 474';
        this.contrato.empleador_departamento = 'LIMA';
        this.contrato.empleador_provincia =  'LIMA';
        this.contrato.empleador_distrito = 'SAN MIGUEL';
      } else {
        this.contrato.empleador_departamento = this.personalData.departamento_empresa || '';
        this.contrato.empleador_provincia = this.personalData.provincia_empresa || '';
        this.contrato.empleador_distrito = this.personalData.distrito_empresa || '';
      }
      
      this.contrato.empleado_nombres = `${this.personalData.primer_nombre || ''} ${this.personalData.segundo_nombre || ''} ${this.personalData.apellido_paterno || ''} ${this.personalData.apellido_materno || ''}`.trim();
      this.contrato.empleado_dni = this.personalData.documento || '';
      this.contrato.empleado_domicilio = this.personalData.direccion || '';
      this.contrato.empleado_departamento = this.personalData.departamento || '';
      this.contrato.empleado_provincia = this.personalData.provincia || '';
      this.contrato.empleado_distrito = this.personalData.distrito || '';
      this.contrato.empleado_cargo = this.personalData.cargo || '';
      this.contrato.sueldo = this.personalData.salario || '';
      this.contrato.genero = this.personalData.genero || '';

      // Fecha de inicio: usar fecha_incorporacion o fecha actual de Chiclayo si no existe
      this.contrato.fecha_inicio = this.personalData.fecha_incorporacion || this.getCurrentDateChiclayo();

      this.contrato.fecha_fin = this.personalData.fecha_cese || '';
      this.contrato.email = this.personalData.email || '';
      this.contrato.empleado_telefono = this.personalData.telefono || '';
      this.contrato.importe = this.personalData.condicion_trabajo_alimentacion || '';
      this.contrato.categoria_contrato = this.personalData.categoria_contrato || '';
    }
  }

  private getCurrentDateChiclayo(): string {
    // Crear fecha actual en zona horaria de Perú (UTC-5)
    const now = new Date();
    const peruTime = new Date(now.getTime() - (5 * 60 * 60 * 1000)); // UTC-5

    // Formatear como YYYY-MM-DD para input type="date"
    const year = peruTime.getFullYear();
    const month = String(peruTime.getMonth() + 1).padStart(2, '0');
    const day = String(peruTime.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  private formatDateToSpanish(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString + 'T00:00:00'); // Add T00:00:00 to avoid timezone issues
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    }).toUpperCase();
  }

  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  crearContrato(): void {
    // Validaciones antes de crear el contrato
    if (!this.personalId || !this.personalData) {
      this.mostrarError('No se encontraron los datos del personal necesarios para generar el contrato.');
      return;
    }

    if (!this.contrato.categoria_contrato) {
      this.mostrarError('Por favor selecciona la categoría del contrato (COMERCIAL o ADMINISTRATIVO).');
      return;
    }

    if (!this.contrato.fecha_inicio || !this.contrato.fecha_fin) {
      this.mostrarError('Por favor completa las fechas de inicio y fin del contrato.');
      return;
    }

    // Validación específica para contratos COMERCIALES
    if (this.contrato.categoria_contrato === 'COMERCIAL') {
      if (!this.contrato.empleado_cantidad_ventas || this.contrato.empleado_cantidad_ventas <= 0) {
        this.mostrarError('Por favor ingresa la cantidad de ventas para el contrato comercial (debe ser mayor a 0).');
        return;
      }
    }

    // Si todas las validaciones pasan, crear el contrato
    let html = this.contratoTemplate;

    // Format dates for the template
    const formattedFechaInicio = this.formatDateToSpanish(this.contrato.fecha_inicio);
    const formattedFechaFin = this.formatDateToSpanish(this.contrato.fecha_fin);

    // Texto específico según el tipo de contrato para el template
    let textoClausulaUno = '';
    if (this.contrato.categoria_contrato === 'COMERCIAL') {
      textoClausulaUno = 'EL EMPLEADOR es una empresa que se encuentra acogida al régimen laboral de la REMYPE con número de registro 0001718610-2019 acreditada MICRO EMPRESA, cuyo objeto social es el Telemarketing.<br>Tanto la empresa Operadora de Telefonía, Internet y Cable, como el Distribuidor que contrata a EL EMPLEADOR, son de España, siendo que el público objetivo está en dicho país. Por lo cual, se requiere de los servicios de EL TRABAJADOR en forma temporal por necesidad de mercado, de acuerdo a la demanda de clientes en dicho país, siendo que incluso las operaciones de la empresa se encuentran sujetas a las necesidades y disposiciones del mercado local del país mencionado.';
    } else if (this.contrato.categoria_contrato === 'ADMINISTRATIVO') {
      textoClausulaUno = 'EL EMPLEADOR es una empresa que se encuentra acogida al régimen laboral de la REMYPE con número de registro 0001718610-2019 acreditada MICRO EMPRESA, cuyo objeto social es la prestación de servicios administrativos y de gestión empresarial. La empresa requiere de los servicios de EL TRABAJADOR para el desarrollo de actividades administrativas, de soporte y gestión interna, de acuerdo a las necesidades operativas y administrativas de la organización.';
    } else {
      textoClausulaUno = 'EL EMPLEADOR es una empresa que se encuentra acogida al régimen laboral de la REMYPE con número de registro 0001718610-2019 acreditada MICRO EMPRESA. Se requiere de los servicios de EL TRABAJADOR de acuerdo a las necesidades de la empresa.';
    }

    // Tratamiento según género para el template
    const tratamientoGenero = this.personalData?.genero === 'M' ? 'Sr' : 'Sra';

    // Create a temporary object with formatted dates and conditional text for replacement
    const contratoForTemplate = {
      ...this.contrato,
      fecha_inicio: formattedFechaInicio,
      fecha_fin: formattedFechaFin,
      texto_clausula_uno: textoClausulaUno,
      tratamiento_genero: tratamientoGenero,
      empleado_cantidad_ventas: (this.contrato.empleado_cantidad_ventas || 7).toString() // Asegurar que sea string
    };

    console.log('contratoForTemplate.empleado_cantidad_ventas:', contratoForTemplate.empleado_cantidad_ventas);

    for (const key in contratoForTemplate) {
      if (contratoForTemplate.hasOwnProperty(key)) {
        const regex = new RegExp(`{{${key}}}`, 'g');
        html = html.replace(regex, contratoForTemplate[key]);
      }
    }
    this.contratoGenerado = this.sanitizer.bypassSecurityTrustHtml(html);
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.contratoGenerado = '';
  }

  // Función para mostrar errores personalizados
  mostrarError(mensaje: string): void {
    console.log('Mostrando error:', mensaje);
    this.errorMessage = mensaje;
    this.showError = true;
    console.log('showError:', this.showError);
    console.log('errorMessage:', this.errorMessage);

    // Forzar detección de cambios
    this.cdr.detectChanges();

    // Auto-ocultar después de 5 segundos
    setTimeout(() => {
      this.ocultarError();
    }, 5000);
  }

  // Función para ocultar error
  ocultarError(): void {
    console.log('Ocultando error');
    this.showError = false;
    this.errorMessage = '';
    this.cdr.detectChanges();
  }

  // Función para mostrar mensajes de éxito
  mostrarExito(mensaje: string, duracion: number = 3000): void {
    console.log('Mostrando éxito:', mensaje);
    this.successMessage = mensaje;
    this.showSuccess = true;
    console.log('showSuccess:', this.showSuccess);

    // Forzar detección de cambios
    this.cdr.detectChanges();

    // Auto-ocultar después del tiempo especificado
    setTimeout(() => {
      this.ocultarExito();
    }, duracion);
  }

  // Función para ocultar mensaje de éxito
  ocultarExito(): void {
    console.log('Ocultando mensaje de éxito');
    this.showSuccess = false;
    this.successMessage = '';
    this.cdr.detectChanges();
  }

  // Métodos de paginación
  updateDisplayedContratos(): void {
    this.totalPages = Math.ceil(this.contratos.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.displayedContratos = this.contratos.slice(startIndex, endIndex);
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updateDisplayedContratos();
    }
  }

  onPageSizeChange(newSize: number): void {
    this.itemsPerPage = newSize;
    this.currentPage = 1; // Reset to first page
    this.updateDisplayedContratos();
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pages.push(i);
    }
    return pages;
  }

  // Función para forzar el cierre del modal de vista previa
  cerrarModalVistaPreviaForzado(): void {
    console.log('Forzando cierre del modal de vista previa...');
    console.log('Estado actual showModal:', this.showModal);

    // Múltiples intentos para asegurar el cierre
    this.showModal = false;
    this.contratoGenerado = '';

    // Forzar detección de cambios inmediatamente
    this.cdr.detectChanges();

    // Primer intento con detección de cambios
    setTimeout(() => {
      this.showModal = false;
      this.contratoGenerado = '';
      this.cdr.detectChanges();
      console.log('Modal cerrado forzadamente, estado showModal:', this.showModal);
    }, 50);

    // Segundo intento
    setTimeout(() => {
      this.showModal = false;
      this.contratoGenerado = '';
      this.cdr.detectChanges();
    }, 150);

    // Tercer intento final
    setTimeout(() => {
      this.showModal = false;
      this.contratoGenerado = '';
      this.cdr.detectChanges();
      console.log('Cierre final del modal, estado showModal:', this.showModal);
    }, 250);
  }

  // Nueva función para generar y guardar contrato
  generarYGuardarContrato(): void {
    try {
      // Las validaciones ya se hicieron en crearContrato()
      // Preparar datos del contrato para enviar al backend
      const contratoData: Contrato = {
        id_personal: parseInt(this.personalId!),
        empleador_razon_social: this.contrato.empleador_razon_social,
        empleador_ruc: this.contrato.empleador_ruc,
        empleador_domicilio: this.contrato.empleador_domicilio,
        empleador_departamento: this.contrato.empleador_departamento,
        empleador_sede: this.contrato.empleador_sede,
        empleador_provincia: this.contrato.empleador_provincia,
        empleador_distrito: this.contrato.empleador_distrito,
        empleado_nombres: this.contrato.empleado_nombres,
        empleado_dni: this.contrato.empleado_dni,
        empleado_domicilio: this.contrato.empleado_domicilio,
        empleado_departamento: this.contrato.empleado_departamento,
        empleado_provincia: this.contrato.empleado_provincia,
        empleado_distrito: this.contrato.empleado_distrito,
        empleado_cargo: this.contrato.empleado_cargo,
        empleado_cantidad_ventas: this.contrato.empleado_cantidad_ventas,
        fecha_inicio: this.contrato.fecha_inicio,
        fecha_fin: this.contrato.fecha_fin,
        sueldo: this.contrato.sueldo,
        numero_letras: this.contrato.numero_letras,
        importe: this.contrato.importe,
        importe_letras: this.importeEnLetras,
        email: this.contrato.email,
        empleado_telefono: this.contrato.empleado_telefono,
        categoria_contrato: this.contrato.categoria_contrato,
        genero: this.personalData?.genero || 'M',
        fecha_firma: this.contrato.fecha_firma
      };

      // Debug: Mostrar datos que se envían
      console.log('Datos del contrato a enviar:', contratoData);

      // Guardar contrato en el backend
      this.contratoService.crearContrato(contratoData).subscribe({
        next: (_contratoGuardado) => {
          console.log('Contrato guardado exitosamente, iniciando descarga automática...');

          // Cargar la lista actualizada de contratos
          this.cargarContratos();

          // Forzar detección de cambios
          this.cdr.detectChanges();

          // Cerrar el modal de vista previa inmediatamente
          this.cerrarModalVistaPreviaForzado();

          // Descargar PDF automáticamente
          setTimeout(() => {
            try {
              this.imprimirContrato();

              // Mostrar mensaje de éxito personalizado
              setTimeout(() => {
                this.notificationService.success('Contrato guardado correctamente y PDF descargado exitosamente');
              }, 500);

            } catch (error) {
              console.error('Error al descargar PDF:', error);
              this.notificationService.error('El contrato se guardó correctamente, pero hubo un problema al descargar el PDF.');
            }
          }, 500);
        },
        error: (error) => {
          console.error('Error al guardar el contrato:', error);

          // Mostrar mensaje de error más específico
          let errorMessage = 'Hubo un problema al guardar el contrato. Por favor, inténtalo de nuevo.';

          if (error.error && typeof error.error === 'string') {
            errorMessage = error.error;
          } else if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          this.notificationService.error(`Error al guardar contrato: ${errorMessage}`);
        }
      });

    } catch (error) {
      console.error('Error al preparar el contrato:', error);
      this.notificationService.error('Hubo un problema al preparar los datos del contrato.');
    }
  }

  // Función para cargar contratos del personal
  cargarContratos(): void {
    if (this.personalId) {
      // Mostrar loading de SweetAlert
      Swal.fire({
        html: `
          <div style="text-align: center; padding: 20px;">
            <h2 style="margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Cargando Contratos</h2>
            <p style="margin: 0 0 20px 0; color: #666;">Obteniendo lista de contratos existentes...</p>
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
          </div>
          <style>
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        `,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        showCancelButton: false,
        showDenyButton: false,
        showCloseButton: false,
        customClass: {
          popup: 'swal2-borderless'
        },
        didOpen: () => {
          // Eliminar completamente la sección de acciones
          const popup = Swal.getPopup();
          if (popup) {
            const actions = popup.querySelector('.swal2-actions');
            if (actions) {
              actions.remove();
            }
            const footer = popup.querySelector('.swal2-footer');
            if (footer) {
              footer.remove();
            }
          }
        }
      });

      this.contratoService.listarContratosPorPersonal(parseInt(this.personalId)).subscribe({
        next: (contratos) => {
          this.contratos = contratos;
          console.log('Contratos cargados:', contratos);

          // Actualizar paginación
          this.updateDisplayedContratos();

          // Forzar detección de cambios para actualizar la vista
          this.cdr.detectChanges();

          // Cerrar loading y mostrar resultado
          Swal.close();

          // Mostrar mensaje de resultado
          if (contratos.length > 0) {
            this.notificationService.success(`Contratos cargados: Se encontraron ${contratos.length} contrato(s)`);
          } else {
            this.notificationService.info('Sin contratos: No se encontraron contratos para este empleado');
          }
        },
        error: (error) => {
          console.error('Error al cargar contratos:', error);

          // Cerrar loading y mostrar error
          Swal.close();

          this.notificationService.error('Hubo un problema al obtener la lista de contratos. Por favor, inténtalo de nuevo.');
        }
      });
    }
  }

  // Función para formatear fechas
  formatearFecha(fecha: string): string {
    if (!fecha) return '';

    try {
      // Dividir la fecha en partes para evitar problemas de zona horaria
      const [year, month, day] = fecha.split('-');

      // Crear fecha usando los componentes directamente (mes es 0-indexado)
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

      return date.toLocaleDateString('es-PE', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error al formatear fecha:', error);
      return fecha;
    }
  }

  // Función para descargar contrato existente
  descargarContratoExistente(contrato: Contrato): void {
    try {
      console.log('Descargando contrato existente:', contrato);

      // Mostrar loading mientras se prepara el contrato
      Swal.fire({
        html: `
          <div style="text-align: center; padding: 20px;">
            <h2 style="margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Preparando Descarga</h2>
            <p style="margin: 0 0 20px 0; color: #666;">Generando PDF del contrato de ${contrato.empleado_nombres}...</p>
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
          </div>
          <style>
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        `,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        showCancelButton: false,
        showDenyButton: false,
        showCloseButton: false,
        customClass: {
          popup: 'swal2-borderless'
        },
        didOpen: () => {
          // Eliminar completamente la sección de acciones
          const popup = Swal.getPopup();
          if (popup) {
            const actions = popup.querySelector('.swal2-actions');
            if (actions) {
              actions.remove();
            }
            const footer = popup.querySelector('.swal2-footer');
            if (footer) {
              footer.remove();
            }
          }
        }
      });

      // Simular un pequeño delay para mostrar el loading
      setTimeout(() => {
        try {
          // Cargar los datos del contrato seleccionado en el formulario
          this.cargarDatosContratoExistente(contrato);

          // Generar y descargar el PDF directamente usando los datos del contrato existente
          this.imprimirContratoExistente(contrato);

          // Cerrar loading y mostrar confirmación
          Swal.close();

          // Mostrar mensaje de confirmación
          setTimeout(() => {
            this.notificationService.success(`PDF descargado: Contrato de ${contrato.empleado_nombres} descargado exitosamente`);
          }, 500);

        } catch (error) {
          // Cerrar loading y mostrar error
          Swal.close();

          console.error('Error al generar PDF:', error);
          this.notificationService.error('Hubo un problema al generar el PDF del contrato.');
        }
      }, 800); // 800ms para mostrar el loading

    } catch (error) {
      console.error('Error al descargar contrato existente:', error);
      this.notificationService.error('Hubo un problema al preparar la descarga del contrato.');
    }
  }

  // Función para cargar datos de un contrato existente en el formulario
  private cargarDatosContratoExistente(contrato: Contrato): void {
    console.log('Cargando datos del contrato existente:', contrato);

    // Cargar todos los datos del contrato en el objeto this.contrato
    this.contrato = {
      empleador_razon_social: contrato.empleador_razon_social,
      empleador_ruc: contrato.empleador_ruc,
      empleador_domicilio: contrato.empleador_domicilio,
      empleador_sede: contrato.empleador_sede,
      empleador_departamento: contrato.empleador_departamento,
      empleador_provincia: contrato.empleador_provincia,
      empleador_distrito: contrato.empleador_distrito,
      empleado_nombres: contrato.empleado_nombres,
      empleado_dni: contrato.empleado_dni,
      empleado_domicilio: contrato.empleado_domicilio,
      empleado_departamento: contrato.empleado_departamento,
      empleado_provincia: contrato.empleado_provincia,
      empleado_distrito: contrato.empleado_distrito,
      empleado_cargo: contrato.empleado_cargo,
      empleado_cantidad_ventas: contrato.empleado_cantidad_ventas,
      fecha_inicio: contrato.fecha_inicio,
      fecha_fin: contrato.fecha_fin,
      sueldo: contrato.sueldo,
      numero_letras: contrato.numero_letras,
      importe: contrato.importe,
      email: contrato.email,
      empleado_telefono: contrato.empleado_telefono,
      categoria_contrato: contrato.categoria_contrato,
      fecha_firma: contrato.fecha_firma
    };

    // Actualizar campos calculados
    this.updateSueldoEnLetras();
    this.updateImporteEnLetras();

    console.log('Datos del contrato cargados en el formulario');
  }



  imprimirContrato(): void {
    const employeeName = [
      this.personalData.primer_nombre,
      this.personalData.segundo_nombre,
      this.personalData.apellido_paterno,
      this.personalData.apellido_materno
    ].filter(Boolean).join(' ').replace(/\s+/g, '_');
    const fileName = `CONTRATO_${employeeName}.pdf`;

    // Generate PDF content from HTML template
    this.generatePdfFromTemplate(fileName);
  }

  // Función específica para imprimir contratos existentes usando datos del JSON
  imprimirContratoExistente(contrato: Contrato): void {
    // Usar el nombre del empleado del contrato existente
    const employeeName = contrato.empleado_nombres.replace(/\s+/g, '_');
    const fileName = `CONTRATO_${employeeName}.pdf`;

    console.log('Generando PDF para contrato existente:', fileName);
    console.log('Datos del contrato:', contrato);

    // Generate PDF content using contract data directly
    this.generatePdfFromTemplateExistente(fileName, contrato);
  }

  private async generatePdfFromTemplate(fileName: string): Promise<void> {
    console.log('Iniciando generatePdfFromTemplate con fileName:', fileName);

    try {
      // Create PDF using jsPDF with text-based approach
      const pdf = new jsPDF('p', 'mm', 'a4');
      console.log('PDF creado exitosamente');

    // Set margins and page dimensions
    const pageWidth = 210;
    const pageHeight = 297;
    const margin = 15;
    const contentWidth = pageWidth - (margin * 2);
    const lineHeight = 6; // Increased line height for better spacing
    let currentY = margin;

    // Helper function to add text with manual justification and auto-bold for uppercase words
    const addText = (text: string, fontSize: number = 12, isBold: boolean = false, isCenter: boolean = false, justify: boolean = true, color: string = '#000000') => {
      pdf.setFontSize(fontSize);
      pdf.setTextColor(color);

      if (isCenter) {
        // For centered text, use simple approach
        pdf.setFont('times', isBold ? 'bold' : 'normal');
        if (currentY + lineHeight > pageHeight - margin) {
          pdf.addPage();
          currentY = margin;
        }
        pdf.text(text, pageWidth / 2, currentY, { align: 'center' });
        currentY += lineHeight + (isBold ? 2 : 1);
        return;
      }

      // Split text into words and detect uppercase words
      const words = text.split(' ');
      const processedWords: Array<{text: string, bold: boolean}> = [];

      for (const word of words) {
        const isUppercase = word === word.toUpperCase() && word.length > 1 && /[A-Z]/.test(word);
        processedWords.push({
          text: word,
          bold: isBold || isUppercase
        });
      }

      // Use addMixedText for proper justification and formatting
      addMixedText(processedWords, fontSize, justify);
    };

    // Helper function to add text with mixed formatting and manual justification
    const addMixedText = (parts: Array<{text: string, bold: boolean}>, fontSize: number = 12, justify: boolean = true) => {
      pdf.setFontSize(fontSize);
      let currentLineParts: Array<{text: string, bold: boolean, width: number}> = [];
      let currentLineWidth = 0;

      // Check if we need a new page
      if (currentY + lineHeight > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }

      const flushLine = (isLastLine: boolean = false) => {
        if (currentLineParts.length === 0) return;

        let x = margin;
        const totalTextWidth = currentLineParts.reduce((sum, part) => sum + part.width, 0);
        const availableSpace = contentWidth - totalTextWidth;
        const gaps = currentLineParts.length - 1;

        // Calculate extra space per gap for justification
        let extraSpacePerGap = 0;
        if (justify && !isLastLine && gaps > 0 && availableSpace > 0) {
          extraSpacePerGap = availableSpace / gaps;
        }

        for (let i = 0; i < currentLineParts.length; i++) {
          const part = currentLineParts[i];
          pdf.setFont('times', part.bold ? 'bold' : 'normal');
          pdf.text(part.text, x, currentY);
          x += part.width;

          // Add extra space between words for justification
          if (i < currentLineParts.length - 1) {
            x += extraSpacePerGap;
          }
        }

        currentY += lineHeight;
        currentLineParts = [];
        currentLineWidth = 0;

        // Check if we need a new page after adding the line
        if (currentY + lineHeight > pageHeight - margin) {
          pdf.addPage();
          currentY = margin;
        }
      };

      // Process all parts and split into words
      const allWords: Array<{text: string, bold: boolean}> = [];

      for (const part of parts) {
        const words = part.text.split(' ');
        for (let i = 0; i < words.length; i++) {
          if (words[i].trim()) {
            // Check if word is uppercase (should be bold)
            const isUppercase = words[i] === words[i].toUpperCase() && words[i].length > 1 && /[A-Z]/.test(words[i]);
            allWords.push({
              text: words[i] + (i < words.length - 1 ? ' ' : ''),
              bold: part.bold || isUppercase
            });
          }
        }
        // Add space between parts if not the last part
        if (part !== parts[parts.length - 1]) {
          allWords.push({ text: ' ', bold: false });
        }
      }

      // Now process words and build lines
      for (const word of allWords) {
        pdf.setFont('times', word.bold ? 'bold' : 'normal');
        const wordWidth = pdf.getTextWidth(word.text);

        // Check if adding this word would exceed the line width
        if (currentLineWidth + wordWidth > contentWidth && currentLineParts.length > 0) {
          flushLine(false);
        }

        currentLineParts.push({
          text: word.text,
          bold: word.bold,
          width: wordWidth
        });
        currentLineWidth += wordWidth;
      }

      // Flush the last line
      flushLine(true);
      currentY += 1; // Small space after paragraph
    };

    // Helper function to add extra space
    const addExtraSpace = (space: number) => {
      currentY += space;
    };

    // Helper function for indented justified text with auto-bold for uppercase
    const addIndentedText = (text: string, fontSize: number = 12, isBold: boolean = false, indentMm: number = 10) => {
      if (currentY + lineHeight > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }

      pdf.setFontSize(fontSize);

      const indentedMargin = margin + indentMm;
      const indentedContentWidth = contentWidth - indentMm;

      // Split text into words
      const words = text.split(' ');
      let lines = [];
      let currentLine = '';

      // Create lines that fit within the indented width
      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + (currentLine ? ' ' : '') + words[i];
        pdf.setFont('times', 'normal'); // Set normal font to measure
        const testWidth = pdf.getTextWidth(testLine);

        if (testWidth > indentedContentWidth && currentLine !== '') {
          lines.push(currentLine);
          currentLine = words[i];
        } else {
          currentLine = testLine;
        }
      }

      if (currentLine) {
        lines.push(currentLine);
      }

      // Print each line with indentation, justification, and auto-bold
      lines.forEach((line, index) => {
        if (currentY + lineHeight > pageHeight - margin) {
          pdf.addPage();
          currentY = margin;
        }

        if (index === lines.length - 1) {
          // Last line - no justification but with auto-bold
          const lineWords = line.split(' ');
          let x = indentedMargin;

          lineWords.forEach((word, wordIndex) => {
            // Auto-bold logic: if word is all uppercase and longer than 2 characters
            const shouldBeBold = isBold || (word.length > 2 && word === word.toUpperCase() && /^[A-ZÁÉÍÓÚÑ]+$/.test(word));
            pdf.setFont('times', shouldBeBold ? 'bold' : 'normal');

            pdf.text(word, x, currentY);
            x += pdf.getTextWidth(word);
            if (wordIndex < lineWords.length - 1) {
              x += pdf.getTextWidth(' ');
            }
          });
        } else {
          // Justify the line manually with auto-bold
          const lineWords = line.split(' ');
          if (lineWords.length > 1) {
            // Calculate total width for justified spacing
            let totalTextWidth = 0;
            lineWords.forEach(word => {
              const shouldBeBold = isBold || (word.length > 2 && word === word.toUpperCase() && /^[A-ZÁÉÍÓÚÑ]+$/.test(word));
              pdf.setFont('times', shouldBeBold ? 'bold' : 'normal');
              totalTextWidth += pdf.getTextWidth(word);
            });

            const totalSpaceWidth = indentedContentWidth - totalTextWidth;
            const spaceWidth = totalSpaceWidth / (lineWords.length - 1);

            let x = indentedMargin;
            lineWords.forEach((word, wordIndex) => {
              const shouldBeBold = isBold || (word.length > 2 && word === word.toUpperCase() && /^[A-ZÁÉÍÓÚÑ]+$/.test(word));
              pdf.setFont('times', shouldBeBold ? 'bold' : 'normal');

              pdf.text(word, x, currentY);
              x += pdf.getTextWidth(word);
              if (wordIndex < lineWords.length - 1) {
                x += spaceWidth;
              }
            });
          } else {
            const shouldBeBold = isBold || (line.length > 2 && line === line.toUpperCase() && /^[A-ZÁÉÍÓÚÑ]+$/.test(line));
            pdf.setFont('times', shouldBeBold ? 'bold' : 'normal');
            pdf.text(line, indentedMargin, currentY);
          }
        }

        currentY += lineHeight;
      });
    };

    // Helper function for indented mixed text (preserves specific bold formatting)
    const addIndentedMixedText = (parts: Array<{text: string, bold: boolean}>, fontSize: number = 12, indentMm: number = 10) => {
      if (currentY + lineHeight > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }

      pdf.setFontSize(fontSize);

      const indentedMargin = margin + indentMm;
      const indentedContentWidth = contentWidth - indentMm;

      // Combine all parts into a single text with formatting info
      let fullText = '';
      let formatMap = new Map(); // Maps character position to bold state
      let currentPos = 0;

      parts.forEach(part => {
        for (let i = 0; i < part.text.length; i++) {
          formatMap.set(currentPos + i, part.bold);
        }
        fullText += part.text;
        currentPos += part.text.length;
      });

      // Split into words while preserving formatting
      const words = fullText.split(' ');
      let lines = [];
      let currentLine = '';
      let currentLineStart = 0;

      // Create lines that fit within the indented width
      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + (currentLine ? ' ' : '') + words[i];
        pdf.setFont('times', 'normal'); // Use normal font for measurement
        const testWidth = pdf.getTextWidth(testLine);

        if (testWidth > indentedContentWidth && currentLine !== '') {
          lines.push({
            text: currentLine,
            startPos: currentLineStart
          });
          currentLineStart += currentLine.length + 1; // +1 for space
          currentLine = words[i];
        } else {
          currentLine = testLine;
        }
      }

      if (currentLine) {
        lines.push({
          text: currentLine,
          startPos: currentLineStart
        });
      }

      // Print each line with indentation and mixed formatting
      lines.forEach((lineInfo, lineIndex) => {
        if (currentY + lineHeight > pageHeight - margin) {
          pdf.addPage();
          currentY = margin;
        }

        const line = lineInfo.text;
        const lineWords = line.split(' ');

        if (lineIndex === lines.length - 1) {
          // Last line - no justification but with mixed formatting
          let x = indentedMargin;
          let charPos = lineInfo.startPos;

          lineWords.forEach((word, wordIndex) => {
            // Check if this word should be bold based on original formatting
            let wordShouldBeBold = false;
            for (let i = 0; i < word.length; i++) {
              if (formatMap.get(charPos + i)) {
                wordShouldBeBold = true;
                break;
              }
            }

            pdf.setFont('times', wordShouldBeBold ? 'bold' : 'normal');
            pdf.text(word, x, currentY);
            x += pdf.getTextWidth(word);
            charPos += word.length;

            if (wordIndex < lineWords.length - 1) {
              x += pdf.getTextWidth(' ');
              charPos += 1; // for space
            }
          });
        } else {
          // Justify the line with mixed formatting
          if (lineWords.length > 1) {
            // Calculate total width for justified spacing
            let totalTextWidth = 0;
            let charPos = lineInfo.startPos;

            lineWords.forEach(word => {
              let wordShouldBeBold = false;
              for (let i = 0; i < word.length; i++) {
                if (formatMap.get(charPos + i)) {
                  wordShouldBeBold = true;
                  break;
                }
              }
              pdf.setFont('times', wordShouldBeBold ? 'bold' : 'normal');
              totalTextWidth += pdf.getTextWidth(word);
              charPos += word.length + 1; // +1 for space
            });

            const totalSpaceWidth = indentedContentWidth - totalTextWidth;
            const spaceWidth = totalSpaceWidth / (lineWords.length - 1);

            let x = indentedMargin;
            charPos = lineInfo.startPos;

            lineWords.forEach((word, wordIndex) => {
              let wordShouldBeBold = false;
              for (let i = 0; i < word.length; i++) {
                if (formatMap.get(charPos + i)) {
                  wordShouldBeBold = true;
                  break;
                }
              }

              pdf.setFont('times', wordShouldBeBold ? 'bold' : 'normal');
              pdf.text(word, x, currentY);
              x += pdf.getTextWidth(word);
              charPos += word.length;

              if (wordIndex < lineWords.length - 1) {
                x += spaceWidth;
                charPos += 1; // for space
              }
            });
          } else {
            // Single word line
            let wordShouldBeBold = false;
            for (let i = 0; i < line.length; i++) {
              if (formatMap.get(lineInfo.startPos + i)) {
                wordShouldBeBold = true;
                break;
              }
            }
            pdf.setFont('times', wordShouldBeBold ? 'bold' : 'normal');
            pdf.text(line, indentedMargin, currentY);
          }
        }

        currentY += lineHeight;
      });
    };



    // Helper function to format dates like "08 de AGOSTO de 2025"
    const formatDate = (dateString: string): string => {
      if (!dateString) return '';

      const date = new Date(dateString + 'T00:00:00'); // Add time to avoid timezone issues
      const day = date.getDate().toString().padStart(2, '0');
      const months = ['ENERO', 'FEBRERO', 'MARZO', 'ABRIL', 'MAYO', 'JUNIO',
                     'JULIO', 'AGOSTO', 'SEPTIEMBRE', 'OCTUBRE', 'NOVIEMBRE', 'DICIEMBRE'];
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${day} de ${month} de ${year}`;
    };

    // Add header with logo and yellow line
    await this.addHeaderWithLogo(pdf, margin, contentWidth, currentY);

    // Update currentY to start content after header
    currentY = margin + 55; // Reduced space for header (was 60)

    // Generate PDF content directly from template structure
    this.generatePdfContentFromTemplate(addText, addMixedText, addExtraSpace, formatDate, addIndentedText, addIndentedMixedText);

    // Save the PDF
    console.log('Guardando PDF con nombre:', fileName);
    pdf.save(fileName);
    console.log('PDF guardado exitosamente');

    } catch (error) {
      console.error('Error en generatePdfFromTemplate:', error);
      throw error;
    }
  }

  private async addHeaderWithLogo(pdf: any, margin: number, contentWidth: number, startY: number): Promise<void> {
    // Add logo on the LEFT side
    const logoX = margin; // Logo at left margin
    const logoY = startY + 5;
    const logoWidth = 55;
    const logoHeight = 20;

    // Company name and RUC in blue, larger size - POSITIONED RIGHT AFTER VERTICAL LINE
    pdf.setFontSize(16);
    pdf.setTextColor('#09205D');
    pdf.setFont('times', 'bold');
    const textX = margin + logoWidth + 10; // Position text 10px from logo (5px for line + 5px spacing)
    pdf.text(this.contrato.empleador_razon_social, textX, startY + 15);
    pdf.text(`RUC: ${this.contrato.empleador_ruc}`, textX, startY + 25);

    // Load and add logo with correct path
    try {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      await new Promise((resolve, reject) => {
        img.onload = () => {
          try {
            pdf.addImage(img, 'PNG', logoX, logoY, logoWidth, logoHeight);
            console.log('Logo added successfully');
            resolve(true);
          } catch (error) {
            console.log('Error adding logo to PDF:', error);
            reject(error);
          }
        };
        img.onerror = () => {
          console.log('Logo not found at ./logo.png');
          reject(new Error('Logo not found'));
        };
        img.src = './Logotipo.png'; // Correct path for public folder
      });
    } catch (error) {
      console.log('Could not load logo, continuing without it:', error);
    }

    // Add VERTICAL line between logo and text - CLOSER TO LOGO
    pdf.setDrawColor('#09205D'); // Same blue color as text
    pdf.setLineWidth(1);
    const verticalLineX = margin + logoWidth + 5; // Only 5px after logo (was 15px)
    pdf.line(verticalLineX, startY + 5, verticalLineX, startY + 30); // Vertical line from top to bottom of header

    // Add dark yellow HORIZONTAL line below - adjusted position
    pdf.setDrawColor('#B8860B'); // Dark yellow color
    pdf.setLineWidth(2);
    pdf.line(margin, startY + 40, margin + contentWidth, startY + 40); // Reduced from +45 to +40

    // Reset text color to black for subsequent content
    pdf.setTextColor('#000000');
  }

  private generatePdfContentFromTemplate(addText: Function, addMixedText: Function, addExtraSpace: Function, formatDate: Function, addIndentedText: Function, addIndentedMixedText: Function): void {
    // Main title (centered, bold)
    addText('CONTRATO DE TRABAJO DE MICROEMPRESA POR NECESIDAD DE MERCADO', 13, true, true);

    // Introduction paragraph
    addText('Conste por el presente documento que se suscribe por duplicado con igual tenor y valor, un contrato de trabajo sujeto a modalidad de necesidades de mercado que al ampar de los artículos 54º y 58º de la Ley de Productividad y Competitividad Laboral, aprobada por Decreto Supremo Nº 003-97-TR, y asimismo bajo los alcances del DS 013-2013_ produce. Y/o y D.S. No. 08-2008-TR; que celebran de una parte:', 12, false, false, true);

    // Parties list
    // Empleador con indentación completa en todo el párrafo
    addIndentedText(`• ${this.contrato.empleador_razon_social} con RUC Nº ${this.contrato.empleador_ruc}, y domicilio en ${this.contrato.empleador_domicilio} del distrito de ${this.contrato.empleador_distrito}, provincia de ${this.contrato.empleador_provincia}, departamento de ${this.contrato.empleador_departamento}; representado por su Titular Gerente MARITZA MIRIAM RAMIREZ AVILA con DNI Nº 40341592, la misma que tiene número de registro en la REMYPE Nº 0001718610-2019; a quien en adelante se denominará EL EMPLEADOR; y, de otra parte.`, 12, false, 10);

    // Trabajador con indentación completa conservando negritas específicas
    // Determinar tratamiento según género
    const tratamiento = this.personalData?.genero === 'M' ? '• Sr. ' : '• Sr. (ita) ';

    addIndentedMixedText([
      { text: tratamiento, bold: false },
      { text: this.contrato.empleado_nombres, bold: true },
      { text: ', DNI Nº ', bold: false },
      { text: this.contrato.empleado_dni, bold: true },
      { text: `, con domicilio en ${this.contrato.empleado_domicilio} distrito de ${this.contrato.empleado_distrito}, provincia de ${this.contrato.empleado_provincia}, departamento de ${this.contrato.empleado_departamento}, a quien en adelante se denominará `, bold: false },
      { text: 'EL TRABAJADOR', bold: true },
      { text: '; en los términos y condiciones siguientes:', bold: false }
    ], 12, 10);

    // Section: LAS PARTES
    addExtraSpace(2); // Margen superior adicional de 1 punto
    addText('LAS PARTES', 12, true, false, false);

    // CLÁUSULA UNO
    addText('CLÁUSULA UNO.', 12, true, false, false);
    // Texto específico según el tipo de contrato
    if (this.contrato.categoria_contrato === 'COMERCIAL') {
      addText('EL EMPLEADOR es una empresa que se encuentra acogida al régimen laboral de la REMYPE con número de registro 0001718610-2019 acreditada MICRO EMPRESA, cuyo objeto social es el Telemarketing. Tanto la empresa Operadora de Telefonía, Internet y Cable, como el Distribuidor que contrata a EL EMPLEADOR, son de España, siendo que el público objetivo está en dicho país. Por lo cual, se requiere de los servicios de EL TRABAJADOR en forma temporal por necesidad de mercado, de acuerdo a la demanda de clientes en dicho país, siendo que incluso las operaciones de la empresa se encuentran sujetas a las necesidades y disposiciones del mercado local del país mencionado.', 12, false, false, true);
    } else {
      // Texto por defecto si no se ha seleccionado categoría
      addText('EL EMPLEADOR es una empresa que se encuentra acogida al régimen laboral de la REMYPE con número de registro 0001718610-2019 acreditada MICRO EMPRESA, cuyo objeto social es el Telemarketing. Tanto la empresa Operadora de Telefonía, Internet y Cable, como el Distribuidor que contrata a EL EMPLEADOR, son de España, siendo que el público objetivo está en dicho país. Por lo cual, se requiere de los servicios de EL TRABAJADOR en forma temporal por necesidad de mercado, de acuerdo a la demanda de clientes en dicho país, siendo que incluso las operaciones de la empresa se encuentran sujetas a las necesidades y disposiciones del mercado local del país mencionado y los requerimientos de la Operadora de Comunicaciones como del Distribuidor.', 12, false, false, true);
    }

    // OBJETIVOS DEL CONTRATO
    addExtraSpace(2); 
    addText('OBJETO DEL CONTRATO', 12, true, false, false);

    if (this.contrato.categoria_contrato === 'COMERCIAL') {
    // CLÁUSULA DOS
    addText('CLÁUSULA DOS.', 12, true, false, false);
    addText('Por el presente documento EL EMPLEADOR contrata bajo la modalidad ya indicada, los servicios de EL TRABAJADOR quien desempeñará el cargo de TELEOPERADOR, por su tiempo de experiencia en el rubro mayor o igual a un año cumpliendo objetivos, en relación con las causas objetivas señaladas en la CLÁUSULA anterior. EL TRABAJADOR se obliga a prestar sus servicios al EMPLEADOR para realizar las siguientes actividades:', 12, false, false, true);

    // Actividades del trabajador indentadas
    const cantidadVentasTexto = this.contrato.empleado_cantidad_ventas || 7;
    const textoGestionVentas = '• Gestión de ventas telefónicas, bajo las directivas de sus jefes o instructores, comprometiéndose EL TRABAJADOR a realizar un mínimo de 10 ventas concretadas al mes. Solo en el primer mes de labores, EL TRABAJADOR tendrá la salvedad de cumplir con una cuota mensual mínima de ' + cantidadVentasTexto + ' ventas concretadas.';
    console.log('Texto gestión ventas:', textoGestionVentas);
    addIndentedText(textoGestionVentas, 12, false, 10);

    addIndentedText('• Las labores que se le impartan por necesidades del servicio en ejercicio de las facultades de administración y dirección de la empresa, de conformidad con el artículo 9º del Texto Único Ordenado de la Ley de Productividad y Competitividad Laboral, aprobado por Decreto Supremo Nº 003-97-TR.', 12, false, 10);

    addText('EL EMPLEADOR está facultado a efectuar modificaciones razonables en función a la capacidad y aptitud de EL TRABAJADOR y a las necesidades y requerimientos de la misma, sin que dichas variaciones signifiquen menoscabo de categoría y/o remuneración.', 12, false, false, true);

    addText('Queda entendido que la prestación de servicios deberá ser efectuada de manera personal, no pudiendo EL TRABAJADOR ser reemplazado ni ayudado por tercera persona.', 12, false, false, true);

    } else{

    addText('Por el presente documento EL EMPLEADOR contrata bajo la modalidad ya indicada, los servicios de EL TRABAJADOR quien desempeñará el cargo de RESPONSABLE DE ENTRENAMIENTO Y CAPACITACIÓN. ', 12, false, false, true);

    addText('CLÁUSULA DOS.', 12, true, false, false);
    addText('por su tiempo de experiencia en el rubro mayor o igual a un año cumpliendo objetivos, en relación con las causas objetivas señaladas en la cláusula anterior. EL TRABAJADOR se obliga a prestar sus servicios al EMPLEADOR para realizar las siguientes actividades:', 12, false, false, true);

    addIndentedText('• Programar y desarrollar sesiones de capacitación para postulantes en temas clave de la empresa.', 12, false, 10);

    addIndentedText('• Asegurar que los participantes adquieran los conocimientos y habilidades necesarios para superar la etapa práctica y operativa.', 12, false, 10);

    addIndentedText('• Diseñar, implementar y actualizar los procesos internos del departamento, asegurando su alineación con los objetivos organizacionales, y mantener en todo momento la confidencialidad de la información, conforme a los principios de ética profesional y buenas prácticas empresariales.', 12, false, 10);

    addIndentedText('• Coordinar con el área de Reclutamiento para garantizar una transición ordenada desde la selección hasta el inicio formal.', 12, false, 10);

    addIndentedText('• Desarrollar manuales, presentaciones y contenidos formativos en coordinación con el área de diseño.', 12, false, 10);

    addIndentedText('• Mantener los materiales actualizados según los cambios en políticas, procedimientos y protocolos de atención.', 12, false, 10);

    addIndentedText('• Asegurar que los contenidos sean accesibles, comprensibles y visualmente atractivos.', 12, false, 10);

    addIndentedText('• Elaborar junto al jefe del área el plan mensual que contemple formaciones para nuevos ingresos y capacitaciones para colaboradores activos.', 12, false, 10);

    addIndentedText('• Definir fechas, temas, responsables y recursos necesarios, alineando el calendario con las metas de desarrollo del equipo y del negocio.', 12, false, 10);

    addIndentedText('• Adaptar el cronograma según las necesidades emergentes de MIDAS SOLUTIONS CENTER.', 12, false, 10);

    addIndentedText('• Coordinar la charla de introducción el primer día de formación para nuevos ingresos.', 12, false, 10);

    addIndentedText('• Explicar de manera clara y estructurada las normas internas, valores corporativos y políticas institucionales.', 12, false, 10);

    addIndentedText('• Facilitar la integración del nuevo personal al ambiente organizacional desde su ingreso.', 12, false, 10);

    addIndentedText('• Realizar capacitaciones en técnicas de ventas, comunicación efectiva, manejo de objeciones y atención al cliente. ', 12, false, 10);

    addIndentedText('• Utilizar metodologías participativas y dinámicas para asegurar un proceso de aprendizaje significativo. ', 12, false, 10);

    addIndentedText('• Monitorear el progreso individual y colectivo durante cada proceso formativo.', 12, false, 10);

    addIndentedText('• Coordinar con los supervisores el acompañamiento y evaluación de los nuevos colaboradores durante su primer mes operativo. ', 12, false, 10);

    addIndentedText('• Mantener comunicación constante para identificar necesidades de refuerzo o ajustes.', 12, false, 10);

    addIndentedText('• Elaborar informes de seguimiento con observaciones y recomendaciones, reportando al jefe inmediato. ', 12, false, 10);

    addIndentedText('• Evaluar mensualmente el desempeño del personal capacitado junto con supervisores y Back Office. ', 12, false, 10);

    addIndentedText('• Analizar indicadores clave de rendimiento (KPI) y reportes operativos.', 12, false, 10);

    addIndentedText('• Proponer mejoras o ajustes en los contenidos y métodos de formación en función de los resultados obtenidos.', 12, false, 10);

    addIndentedText('• Elaborar y enviar informes detallados de cada formación realizada, incluyendo: Fechas y contenidos impartidos, Asistencia de participantes, Resultados de evaluaciones, Número de altas generadas, etc. ', 12, false, 10);

    addIndentedText('• Asegurar la revisión y validación de los reportes por parte del jefe del área. ', 12, false, 10);

    addIndentedText('• Mantener actualizados los registros físicos y digitales de los procesos formativos. ', 12, false, 10);

    addIndentedText('• Coordinar permanentemente con Reclutamiento, Recursos Humanos, Supervisores y Back Office para el correcto desarrollo de las capacitaciones. ', 12, false, 10);

    addIndentedText('• Garantizar la disponibilidad de espacios, recursos logísticos y tecnológicos necesarios para la formación. ', 12, false, 10);

    addIndentedText('• Adaptar las capacitaciones a los requerimientos específicos de cada campaña o sede.', 12, false, 10);

    addIndentedText('• Gestionar con la debida anticipación la entrega de cargo en casos de vacaciones, cese o ausencias prolongadas, mediante acta formal de entrega y comunicación por correo electrónico a la persona designada para asumir temporalmente las funciones, garantizando la continuidad operativa del área. ', 12, false, 10);

    addIndentedText('• Ejecutar todas aquellas funciones complementarias asignadas por la Jefatura de Capacitación, Dirección de Capital Humano o Gerencia, cumpliendo con eficiencia, puntualidad y criterio profesional. ', 12, false, 10);

    addIndentedText('• Promover una cultura de respeto, empatía y comunicación asertiva entre los equipos. ', 12, false, 10);

    addIndentedText('• En caso de ser necesario, se deberá coordinar un reemplazo que cuente con la autorización del área de recursos humanos, garantizando que el puesto nunca quede desatendido. ', 12, false, 10);

    addIndentedText('• Apoyar en otras tareas de seguridad según las asignaciones del superior inmediato. ', 12, false, 10);

    addIndentedText('Las labores que se le impartan por necesidades del servicio en ejercicio de las facultades de administración y dirección de la empresa, de conformidad con el artículo 9º del Texto Único Ordenado de la Ley de Productividad y Competitividad Laboral, aprobado por Decreto Supremo Nº 003-97-TR. ', 12, false, false, true);

    addIndentedText('EL EMPLEADOR está facultado a efectuar modificaciones razonables en función a la capacidad y aptitud de EL TRABAJADOR y a las necesidades y requerimientos de la misma, sin que dichas variaciones signifiquen menoscabo de categoría y/o remuneración.  ', 12, false, false, true);

    addIndentedText('Queda entendido que la prestación de servicios deberá ser efectuada de manera personal, no pudiendo EL TRABAJADOR ser reemplazado ni ayudado por tercera persona.', 12, false, false, true);

    }

    // DURACIÓN DEL CONTRATO
    addExtraSpace(2); 
    addText('DURACIÓN DEL CONTRATO', 12, true, false, false);

    addText('CLÁUSULA TRES.', 12, true, false, false);
    addMixedText([
      { text: 'El presente contrato empezará a regir del ', bold: false },
      { text: formatDate(this.contrato.fecha_inicio), bold: true },
      { text: ' y concluirá el ', bold: false },
      { text: formatDate(this.contrato.fecha_fin), bold: true },
      { text: ' salvo que entre ambas partes se pacte la renovación del mismo, de acuerdo a lo estipulado del artículo 10 y 75 de la Ley de Productividad y Competitividad Laboral (D.S. 003-97-TR).', bold: false }
    ], 12, true);

    addExtraSpace(2); 
    addText('CLÁUSULA CUATRO.', 12, true, false, false);
    addMixedText([
      { text: 'De tal forma, al amparo del artículo 10 del Texto Único Ordenado de la Ley de Productividad y Competitividad Laboral, Decreto Legislativo 728 aprobado por Decreto Supremo 003-97 TR, se precisa que el ', bold: false },
      { text: 'período de prueba será de 03 meses', bold: true },
      { text: '.', bold: false }
    ], 12, true);

    addText('Queda entendido que EL EMPLEADOR no está obligado a dar aviso alguno adicional referente al término del presente contrato, operando su extinción en la fecha de su vencimiento, conforme a la presente CLÁUSULA, oportunidad en la cual se abonará al TRABAJADOR los beneficios sociales, que le pudieran corresponder de acuerdo a Ley.', 12, false, false, true);

    // JORNADA ORDINARIA
    addExtraSpace(2); 
    addText('JORNADA ORDINARIA', 12, true, false, false);

    addText('CLÁUSULA CINCO.', 12, true, false, false);
    addText('EL TRABAJADOR observará el horario de trabajo siguiente:', 12, false, false, true);

    if (this.contrato.categoria_contrato === 'COMERCIAL') {
    // Horario indentado
    addIndentedText('De lunes a viernes:', 12, false, 10);
    addIndentedText('• Ingreso 9:00 am', 12, false, 10);
    addIndentedText('• Salida 6:30 pm', 12, false, 10);
    addIndentedText('• Break 60 min', 12, false, 10);
    addIndentedText('Sábados:', 12, false, 10);
    addIndentedText('• Ingreso 9:00 am', 12, false, 10);
    addIndentedText('• Salida 2:30 pm', 12, false, 10);
    
  } else{

    addIndentedText('De lunes a viernes:', 12, false, 10);
    addIndentedText('• Ingreso 7:00 am', 12, false, 10);
    addIndentedText('• Salida 3:45 pm', 12, false, 10);

    addText('De común acuerdo las partes han pactado que el horario de refrigerio será de 45 minutos, siendo gozados de 1:45 PM A 2: 30 PM. ', 12, false, false, true);

    addText('En uso de sus facultades directrices, EL EMPLEADOR está facultado a efectuar modificaciones razonables en la jornada de trabajo de acuerdo a sus necesidades operativas respetando el máximo legal de 48 horas semanales, sin que dichas variaciones signifiquen menoscabo de categoría y/o remuneración. ', 12, false, false, true);

  }

    addText('En uso de sus facultades directrices, EL EMPLEADOR está facultado a efectuar modificaciones razonables en la jornada de trabajo de acuerdo a sus necesidades operativas respetando el máximo legal de 48 horas semanales, sin que dichas variaciones signifiquen menoscabo de categoría y/o remuneración.', 12, false, false, true);

    // OBLIGACIONES DEL EMPLEADOR Y TRABAJADOR
    addExtraSpace(2); 
    addText('OBLIGACIONES DEL EMPLEADOR Y TRABAJADOR', 12, true, false, false);

    addText('CLÁUSULA SEIS.', 12, true, false, false);

     if (this.contrato.categoria_contrato === 'COMERCIAL') {
    addText('EL TRABAJADOR tendrá derecho a 15 días de vacaciones por cada año laborado. Conforme al Artículo 50 DS 013-2013 - Produce. Y/O y D.S. No. 08-2008-TR.', 12, false, false, true);

    // Debug: verificar valor de cantidad de ventas
    console.log('Valor de empleado_cantidad_ventas:', this.contrato.empleado_cantidad_ventas);
    console.log('Tipo:', typeof this.contrato.empleado_cantidad_ventas);

    const cantidadVentas = this.contrato.empleado_cantidad_ventas || 7; // Valor por defecto si está vacío
    console.log('Cantidad de ventas a usar:', cantidadVentas);

    // Construir el texto con concatenación en lugar de template literals
    const textoVentas = 'a. De igual forma, EL EMPLEADOR se compromete a brindar las capacitaciones y medios suficientes para que EL TRABAJADOR pueda cumplir con su cuota mensual mínima de 10 ventas concretadas. Solo en el primer mes de labores, el TRABAJADOR tendrá la salvedad de cumplir con una cuota mensual mínima de ' + cantidadVentas + ' ventas concretadas.';
    console.log('Texto final a mostrar:', textoVentas);

    addText(textoVentas, 12, false, false, true);

    addText('b. Asimismo, EL TRABAJADOR deberá cumplir con las normas propias del Centro de Trabajo, cumplir con las obligaciones y prohibiciones señaladas en la Ley de Productividad y Competitividad Laboral, así como las contenidas en el Reglamento Interno de Trabajo de la empresa, cumplir las políticas de seguridad y salud en el trabajo y demás políticas que exija EL EMPLEADOR. Cualquier violación a dichas normas, el plan para vigilancia, prevención y control del COVID-19 en el trabajo y directivas acarreará el correspondiente accionar de la empresa dentro del marco legal.', 12, false, false, true);

    addText('c. EL TRABAJADOR se compromete, igualmente, a mantener en secreto toda información que llegue a su conocimiento en relación a los negocios de EL EMPLEADOR, sus asociados y/o clientes. Esta obligación subsistirá aun después de terminada la relación laboral y su incumplimiento genera la correspondiente responsabilidad por daños y perjuicios, sin desmedro de la persecución penal por el delito previsto en el artículo 165 del Código Penal o los que correspondan.', 12, false, false, true);

    addText('d. Dado que el TRABAJADOR y EMPLEADOR reconocen que, durante la vigencia del contrato de trabajo, existe riesgo de desviación o aprovechamiento de los contactos o conocimientos a los que el TRABAJADOR ha obtenido acceso gracias a la prestación de servicios con el EMPLEADOR, en adelante know how; en razón a ello, EL TRABAJADOR se compromete a no ser parte en otros', 12, false, false, true);

    addText('e. contratos laborales o de prestación de servicios de manera directa o indirecta con cliente de EL EMPLEADOR, pues ambas partes reconocen que ello implica una concurrencia desleal en contra de EL EMPLEADOR, siendo que, si falta a dicho compromiso, el TRABAJADOR se compromete al pago de una indemnización por daños y perjuicios correspondiente, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. El incumplimiento del mismo, igualmente configura falta grave de despido conforme al inciso a) del artículo 25 del DS N° 001-97-TR.', 12, false, false, true);

    addText('f. EL TRABAJADOR se compromete a, durante la prestación del vínculo laboral o luego de concluida la relación laboral, a resguardar el deber de confidencialidad y en ese sentido a no utilizar ni divulgar la base de datos de la empresa, sistemas, métodos de mercado y estrategias, información confidencial de los clientes, etc, para beneficio propio o de terceros; siendo que si falta a dicho compromiso, el TRABAJADOR se compromete al pago de una indemnización por daños y perjuicios correspondiente, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. Esta infracción al deber de confidencialidad asimismo produciría la configuración de Tráfico Ilegal de Datos Personales, previsto en el artículo 154-A del Código Penal y que tiene una pena prevista de 02 a 05 años de pena privativa de libertad.', 12, false, false, true);

    addText('g. EL TRABAJADOR se compromete a no iniciar como socio, director, gerente, propietario o cualquier otro símil, una empresa o negocio a nivel local, regional o nacional, que tenga el mismo giro que el EMPLEADOR, utilizando la información reservada o confidencial de la empresa, como base de datos, sistemas, métodos de mercado y estrategas, información confidencial de los clientes, etc; siendo que, de faltar a dicho compromiso el TRABAJADOR se compromete al pago de una indemnización por daños y perjuicios correspondiente o mayor a esta, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. Esta infracción al deber de confidencialidad asimismo produciría la configuración del delito de Administración Fraudulenta de Persona Jurídica, previsto y sancionado en el artículo 198 inciso 6 del Código Penal que contempla una sanción de uno a cuatro años de pena privativa de libertad.', 12, false, false, true);

    addText('h. EL TRABAJADOR se compromete a:', 12, false, false, true);
        // Obligaciones del trabajador indentadas
        addIndentedText('• Tener Responsabilidad y Proactividad en el desempeño de su trabajo.', 12, false, 10);
        addIndentedText('• Desarrollar sus actividades con Capacidad de trabajo en equipo.', 12, false, 10);
        addIndentedText('• Presentar Puntualidad en su asistencia.', 12, false, 10);
        addIndentedText('• Presentar Compromiso en sus labores.', 12, false, 10);
        addIndentedText('• Realizar su trabajo con Sentido de seguridad.', 12, false, 10);
        addIndentedText('• Presentar Disposición al trabajo.', 12, false, 10);
        addIndentedText('• Conservar al mínimo las Relaciones interpersonales con sus compañeros por posible fuga de bases de datos e información concedida para fines laborales.', 12, false, 10);
        addIndentedText('• No sostener relaciones de pareja con sus superiores o personal administrativo de la empresa.', 12, false, 10);

     } else{

       addText('LAS PARTES asumen los siguientes compromisos:', 12, false, false, true);

       addIndentedText('a.  EL TRABAJADOR tendrá derecho a 15 días de vacaciones por cada año laborado. Conforme al Artículo 50 DS 013-2013 - Produce. Y/O   y D.S. No. 08-2008-TR. ', 12, false, 10);

       addIndentedText('b.  EL EMPLEADOR cumplirá con todas las leyes laborales vigentes, así como cumplirá con el aseguramiento de salud y seguro vida ley del TRABAJADOR. ', 12, false, 10);

       addIndentedText('c. EL TRABAJADOR deberá cumplir con las normas propias del Centro de Trabajo, cumplir con las obligaciones y prohibiciones señaladas en la Ley de Productividad y Competitividad Laboral, así como las contenidas en el Reglamento Interno de Trabajo de la empresa, cumplir las políticas de seguridad y salud en el trabajo y demás políticas que exija EL EMPLEADOR. Cualquier violación a dichas normas, el plan para vigilancia, prevención y control del COVID-19 en el trabajo y directivas acarreará el correspondiente accionar de la empresa dentro del marco legal. ', 12, false, 10);

       addIndentedText('d. EL TRABAJADOR se compromete, igualmente, a mantener en secreto toda información que llegue a su conocimiento en relación a los negocios de EL EMPLEADOR, sus asociados y/o clientes. Esta obligación subsistirá aun después de terminada la relación laboral y su incumplimiento genera la correspondiente responsabilidad por daños y perjuicios, sin desmedro de la persecución penal por el delito previsto en el artículo 165 del Código Penal o los que correspondan. ', 12, false, 10);

       addIndentedText('e. Dado que el TRABAJADOR y EMPLEADOR reconocen que, durante la vigencia del contrato de trabajo, existe riesgo de desviación o aprovechamiento de los contactos o conocimientos a los  que el TRABAJADOR ha obtenido acceso gracias a la prestación de servicios con el EMPLEADOR, en adelante know how; en razón a ello, EL TRABAJADOR se compromete a no ser parte en otros contratos laborales o de prestación de servicios de manera directa o indirecta con cliente de EL EMPLEADOR, pues ambas partes reconocen que ello implica una concurrencia desleal en contra de EL EMPLEADOR, siendo que, si falta a dicho compromiso, el TRABAJADOR se compromete al pago de una indemnización por daños y perjuicios correspondiente, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. El incumplimiento  del mismo, igualmente configura falta grave de despido conforme al inciso a) del artículo 25 del DS N° 001-97-TR. ', 12, false, 10);

       addIndentedText('f. EL TRABAJADOR se compromete a, durante la prestación del vínculo laboral o luego de concluida la relación laboral, a resguardar el deber de confidencialidad y en ese sentido a no utilizar ni divulgar la base de datos de la empresa, sistemas, métodos de mercado y estrategias, información confidencial de los clientes, etc, para beneficio propio o de terceros; siendo que si falta a dicho compromiso, el TRABAJADOR se compromete al pago de una indemnización por daños y perjuicios correspondiente, sin desmedro de repetir el perjuicio económico que pudiera causar  las faltas de confidencialidad del caso. Esta infracción al deber de confidencialidad asimismo produciría la configuración de Tráfico Ilegal de Datos Personales, previsto en el artículo 154-A del Código Penal y que tiene una pena prevista de 02 a 05 años de pena privativa de libertad.', 12, false, 10);

       addIndentedText('g. EL TRABAJADOR se compromete a no iniciar como socio, director, gerente, propietario o cualquier otro símil, una empresa o negocio a nivel local, regional o nacional, que tenga el mismo giro que el EMPLEADOR, utilizando la información reservada o confidencial de la empresa, como base de datos, sistemas, métodos de mercado y estrategias, información confidencial de los clientes, etc; siendo que, de faltar a dicho compromiso el TRABAJADOR se compromete al pago de una indemnización por daños y perjuicios correspondiente o mayor a esta, sin desmedro de repetir el perjuicio económico que pudiera causar las faltas de confidencialidad del caso. Esta infracción al deber de confidencialidad asimismo produciría la configuración del delito de Administración Fraudulenta de Persona Jurídica, previsto y sancionado en el artículo 198 inciso 6 del Código Penal que contempla una sanción de uno a cuatro años de pena privativa de libertad.', 12, false, 10);

       addText('h. EL TRABAJADOR se compromete a:', 12, false, false, true);
        // Obligaciones del trabajador indentadas
        addIndentedText('• Tener Responsabilidad y Proactividad en el desempeño de su trabajo.', 12, false, 10);
        addIndentedText('• Desarrollar sus actividades con Capacidad de trabajo en equipo.', 12, false, 10);
        addIndentedText('• Presentar Puntualidad en su asistencia.', 12, false, 10);
        addIndentedText('• Presentar Compromiso en sus labores.', 12, false, 10);
        addIndentedText('• Realizar su trabajo con Sentido de seguridad.', 12, false, 10);
        addIndentedText('• Presentar Disposición al trabajo.', 12, false, 10);
        addIndentedText('• Conservar al mínimo las Relaciones interpersonales con sus compañeros por posible fuga de bases de datos e información concedida para fines laborales.', 12, false, 10);
        addIndentedText('• No sostener relaciones de pareja con sus superiores o personal administrativo de la empresa.', 12, false, 10);


     }
    // REMUNERACIÓN
    addExtraSpace(2); 
    addText('REMUNERACIÓN', 12, true, false, false);

    if (this.contrato.categoria_contrato === 'COMERCIAL') {

    addText('CLÁUSULA SIETE.', 12, true, false, false);
    addText(`EL EMPLEADOR abonará al TRABAJADOR la cantidad de S/${this.contrato.sueldo} soles (Mil ciento treinta soles) como remuneración mensual, de la cual se deducirá las aportaciones y descuentos por tributos establecidos en la ley que le resulten aplicables. De igual forma EL EMPLEADOR cancelará a favor de EL TRABAJADOR la suma de S/${this.contrato.importe} soles (Doscientos setenta soles) como concepto no remunerativo de prestaciones alimentarias otorgadas bajo la modalidad de suministro indirecto, precisando que la misma es condición de trabajo sujeta a la asistencia a las labores, toda vez que la empresa tiene horario laboral corrido, teniendo de intermedio el refrigerio entre las horas de prestación efectiva de labores.`, 12, false, false, true);

    } else{

      addText('CLÁUSULA SIETE.', 12, true, false, false);
      addText(`EL EMPLEADOR abonará al TRABAJADOR la cantidad de S/${this.contrato.sueldo} soles (Mil ciento treinta soles) como remuneración mensual, de la cual se deducirá las aportaciones y descuentos por tributos establecidos en la ley que le resulten aplicables.`, 12, false, false, true);

    }

    addExtraSpace(2); 
    addText('CLÁUSULA OCHO.', 12, true, false, false);

    if (this.contrato.categoria_contrato === 'COMERCIAL') {

    addText('Las ausencias injustificadas por parte de EL TRABAJADOR implican la pérdida de la remuneración proporcionalmente a la duración de dicha ausencia, sin perjuicio del ejercicio de las facultades disciplinarias propias de EL EMPLEADOR previstas en la legislación laboral y normas internas de la empresa.', 12, false, false, true);

    }else{

    addText('De igual forma EL EMPLEADOR cancelará a favor de EL TRABAJADOR la suma de S/${this.contrato.importe} soles (Doscientos setenta soles) como concepto no remunerativo de prestaciones alimentarias otorgadas bajo la modalidad de suministro indirecto, precisando que la misma es condición de trabajo sujeta a la asistencia a las labores, toda vez que la empresa tiene horario laboral corrido, teniendo de intermedio el refrigerio entre las horas de prestación efectiva de labores.', 12, false, false, true);

    // RESOLUCIÓN DEL CONTRATO
    }

    if (this.contrato.categoria_contrato === 'ADMINISTRATIVO') {
    addText('CLÁUSULA NUEVE.', 12, true, false, false); 
    addText('Las ausencias injustificadas por parte de EL TRABAJADOR implican la pérdida de la remuneración proporcionalmente a la duración de dicha ausencia, sin perjuicio del ejercicio de las facultades disciplinarias propias de EL EMPLEADOR previstas en la legislación laboral y normas internas de la empresa.', 12, false, false, true);
    
    }

    if (this.contrato.categoria_contrato === 'COMERCIAL') {
    // RESOLUCIÓN DEL CONTRATO
    addExtraSpace(2); 
    addText('RESOLUCIÓN DEL CONTRATO', 12, true, false, false);

    addText('CLÁUSULA NUEVE.', 12, true, false, false);
    addText('Queda entendido que EL EMPLEADOR no está obligado a dar aviso adicional referente al término del presente contrato, operando su extinción en la fecha de su vencimiento conforme la CLÁUSULA tercera, Sistema Normativo de Información Laboral, oportunidad en la cual se abonará al TRABAJADOR los beneficios sociales que le pudieran corresponder de acuerdo a ley. De igual forma, EL EMPLEADOR tiene la facultad de iniciar el respectivo procedimiento disciplinario e imponer las sanciones correspondientes, en tanto a las normas vigentes. A efectos de la aplicación del artículo 24°, 25° y Siguientes del DL 728 también se entenderán como faltas graves las siguientes:', 12, false, false, true);

    addText('A. Sustraer mobiliario o producto de las instalaciones de EL EMPLEADOR sin autorización expresa de este.', 12, false, false, true);
    addText('B. EL TRABAJADOR se compromete a mantener en secreto toda la información de cualquier índole, que llegue a su conocimiento en relación a los negocios de EL EMPLEADOR, sus socios y/o clientes. Esta obligación subsistirá aun después de terminada la relación laboral y su incumplimiento genera la correspondiente responsabilidad por daños y perjuicios, así como la responsabilidad penal por el delito previsto en el artículo 165 del Código Penal.', 12, false, false, true);
    addText('C. Truncar de manera deliberada las ventas asignadas a su cargo.', 12, false, false, true);

    }

    if (this.contrato.categoria_contrato === 'ADMINISTRATIVO') {
    // RESOLUCIÓN DEL CONTRATO
    addExtraSpace(2); 
    addText('RESOLUCIÓN DEL CONTRATO', 12, true, false, false);

    addText('CLÁUSULA DIEZ.', 12, true, false, false);
    addText('Queda entendido que EL EMPLEADOR no está obligado a dar aviso adicional referente al término del presente contrato, operando su extinción en la fecha de su vencimiento conforme la CLÁUSULA tercera, Sistema Normativo de Información Laboral, oportunidad en la cual se abonará al TRABAJADOR los beneficios sociales que le pudieran corresponder de acuerdo a ley. De igual forma, EL EMPLEADOR tiene la facultad de iniciar el respectivo procedimiento disciplinario e imponer las sanciones correspondientes, en tanto a las normas vigentes. A efectos de la aplicación del artículo 24°, 25° y Siguientes del DL 728 también se entenderán como faltas graves las siguientes:', 12, false, false, true);

    addText('A. Sustraer mobiliario o producto de las instalaciones de EL EMPLEADOR sin autorización expresa de este.', 12, false, false, true);
    addText('B. EL TRABAJADOR se compromete a mantener en secreto toda la información de cualquier índole, que llegue a su conocimiento en relación a los negocios de EL EMPLEADOR, sus socios y/o clientes. ', 12, false, false, true);
    addText('C. Esta obligación subsistirá aun después de terminada la relación laboral y su incumplimiento genera la correspondiente responsabilidad por daños y perjuicios, así como la responsabilidad penal por el delito previsto en el artículo 165 del Código Penal.', 12, false, false, true);
    
    }
    // LEGISLACION APLICABLE
    addExtraSpace(2); 

    if (this.contrato.categoria_contrato === 'COMERCIAL') {

    addText('LEGISLACION APLICABLE', 12, true, false, false);

    addText('CLÁUSULA DIEZ.', 12, true, false, false);
    addText('Este contrato queda sujeto a las disposiciones que contiene el Texto Único Ordenado del D.Leg. Nº 728 aprobado por D.S. Nº 003-97-TR - Ley de Productividad y Competitividad Laboral, y de manera específica los beneficios laborales están regulados por DS 013-2013_ Produce. Y/O y D.S. No. 08-2008-TR y demás normas legales que lo regulen o que sean dictadas durante la vigencia del contrato.', 12, false, false, true);

    }else{

    addText('LEGISLACION APLICABLE', 12, true, false, false);

    addText('CLÁUSULA ONCE.', 12, true, false, false);
    addText('Este contrato queda sujeto a las disposiciones que contiene el Texto Único Ordenado del D.Leg. Nº 728 aprobado por D.S. Nº 003-97-TR - Ley de Productividad y Competitividad Laboral, y de manera específica los beneficios laborales están regulados por DS 013-2013_ Produce. Y/O y D.S. No. 08-2008-TR y demás normas legales que lo regulen o que sean dictadas durante la vigencia del contrato.', 12, false, false, true);

    }

    addExtraSpace(2); 

    if (this.contrato.categoria_contrato === 'COMERCIAL') {

      // EXTINCIÓN LABORAL POR CASO FORTUITO Y FUERZA MAYOR
    addText('EXTINCIÓN LABORAL POR CASO FORTUITO Y FUERZA MAYOR', 12, true, false, false);

    addText('CLÁUSULA ONCE.', 12, true, false, false);
    addText('En caso de que ocurra un acontecimiento imprevisible, irresistible e inevitable que haga imposible la continuación de las labores, y en especial la pandemia del Coronavirus y otras de similares características que puedan surgir, tanto EL EMPLEADOR como EL TRABAJADOR acuerdan que de darse ese hecho especificado se procederá a EXTINGUIR los efectos del contrato de trabajo, de conformidad con lo prescrito por los artículos 12 literal l) y 15 del Texto Único Ordenado (TUO) del Decreto Legislativo N° 728, Ley de Productividad y Competitividad Laboral.', 12, false, false, true);

    } else{

      // EXTINCIÓN LABORAL POR CASO FORTUITO Y FUERZA MAYOR
    addText('EXTINCIÓN LABORAL POR CASO FORTUITO Y FUERZA MAYOR', 12, true, false, false);

    addText('CLÁUSULA DOCE.', 12, true, false, false);
    addText('En caso de que ocurra un acontecimiento imprevisible, irresistible e inevitable que haga imposible la continuación de las labores, y en especial la pandemia del Coronavirus y otras de similares características que puedan surgir, tanto EL EMPLEADOR como EL TRABAJADOR acuerdan que de darse ese hecho especificado se procederá a EXTINGUIR los efectos del contrato de trabajo, de conformidad con lo prescrito por los artículos 12 literal l) y 15 del Texto Único Ordenado (TUO) del Decreto Legislativo N° 728, Ley de Productividad y Competitividad Laboral.', 12, false, false, true);

    }


    addExtraSpace(2);
    if (this.contrato.categoria_contrato === 'COMERCIAL') {
      addText('NOTIFICACIÓN ELECTRÓNICA', 12, true, false, false);

      addText('CLÁUSULA DOCE.', 12, true, false, false);
      addText('EL EMPLEADOR y EL TRABAJADOR que suscriben la presente CLÁUSULA, otorgan su conformidad para que EL TRABAJADOR sea notificado a la cuenta de correo electrónico que se detalla a continuación, con las decisiones que EL EMPLEADOR pueda adoptar respecto a contenidos o', 12, false, false, true);

      addExtraSpace(2);
      addText('CLÁUSULA TRECE.', 12, true, false, false);
      addText('procedimientos que estén relacionados con la ejecución del contrato de trabajo celebrado entre ambos. Se entienden que están incorporadas dentro de los alcances de la presente CLÁUSULA las comunicaciones disciplinarias, de amonestaciones, suspensiones o que tengan que ver incluso con el pre aviso de despido y el despido.', 12, false, false, true);

      addText(`Correo Electrónico: `, 12, false, false, false);

      addText(`Las partes acuerdan que los efectos de las notificaciones a la dirección electrónica señalada surten efectos a partir del día siguiente de notificada la decisión o contenido. Si el día siguiente a la notificación electrónica fuese un día inhábil, se entenderá que la notificación se realizó el día hábil inmediato posterior a la notificación electrónica. EL TRABAJADOR acepta las condiciones y efectos de la notificación electrónica que por la presente CLÁUSULA se prevé. En caso que Empleador notifique la misma decisión o procedimiento a través de la notificación electrónica y también a través de la notificación física (postal), se entiende que EL TRABAJADOR quedará notificado con el contenido, decisión o procedimiento a partir del día siguiente de la notificación física. El TRABAJADOR se obliga a informar al EMPLEADOR ya sea por medio escrito o digital, si cambie de dirección mail o correo electrónico, de lo contrario, se reputará por vigente la establecida en el presente contrato. En adición se mandará una alerta vía WhatsApp respecto de la remisión de la notificación electrónica al correo, registrándose para ello, el número del trabajador.`, 12, false, false, true);

    } else {
      addText('NOTIFICACIÓN ELECTRÓNICA', 12, true, false, false);

      addText('CLÁUSULA TRECE.', 12, true, false, false);
      addText('EL EMPLEADOR y EL TRABAJADOR que suscriben la presente CLÁUSULA, otorgan su conformidad para que EL TRABAJADOR sea notificado a la cuenta de correo electrónico que se detalla a continuación, con las decisiones que EL EMPLEADOR pueda adoptar respecto a contenidos o', 12, false, false, true);

      addText(`Correo Electrónico: ${this.contrato.email}`, 12, false, false, false);

      addText(`Las partes acuerdan que los efectos de las notificaciones a la dirección electrónica señalada surten efectos a partir del día siguiente de notificada la decisión o contenido. Si el día siguiente a la notificación electrónica fuese un día inhábil, se entenderá que la notificación se realizó el día hábil inmediato posterior a la notificación electrónica. EL TRABAJADOR acepta las condiciones y efectos de la notificación electrónica que por la presente CLÁUSULA se prevé. En caso que Empleador notifique la misma decisión o procedimiento a través de la notificación electrónica y también a través de la notificación física (postal), se entiende que EL TRABAJADOR quedará notificado con el contenido, decisión o procedimiento a partir del día siguiente de la notificación física. El TRABAJADOR se obliga a informar al EMPLEADOR ya sea por medio escrito o digital, si cambie de dirección mail o correo electrónico, de lo contrario, se reputará por vigente la establecida en el presente contrato. En adición se mandará una alerta vía WhatsApp respecto de la remisión de la notificación electrónica al correo, registrándose para ello, el número del trabajador ${this.contrato.empleado_telefono}.`, 12, false, false, true);
    }

    // RECOMENDACIONES DE SEGURIDAD Y SALUD EN EL TRABAJO
    addExtraSpace(2); 
    addText('RECOMENDACIONES DE SEGURIDAD Y SALUD EN EL TRABAJO', 12, true, false, false);

    addText('CLÁUSULA CATORCE.', 12, true, false, false);
    addText('EL TRABAJADOR deberá seguir las siguientes recomendaciones en materia de seguridad y salud en el trabajo:', 12, false, false, true);

    // Recomendaciones de seguridad indentadas
    addIndentedText('A. Cumplir con el reglamento interno de seguridad y salud en el trabajo dentro de las instalaciones de EL EMPLEADOR.', 12, false, 10);
    addIndentedText('B. Cumplir con los procedimientos de inducción en seguridad y salud en el trabajo antes del ingreso a las instalaciones de EL EMPLEADOR; Así mismo participar de las capacitaciones y entrenamientos en los procedimientos que tiene que ejecutar para el cumplimiento de sus funciones y responsabilidades.', 12, false, 10);
    addIndentedText('C. Informarse sobre la conformación del comité de seguridad y salud en el trabajo de su unidad, quienes son sus representantes y cuáles son sus funciones y responsabilidades.', 12, false, 10);
    addIndentedText('D. Utilizar el equipo de protección personal necesario para la protección de la integridad y salud durante la ejecución de trabajos bajo su responsabilidad.', 12, false, 10);
    addIndentedText('E. Participar del a capacitación y entrenamiento para su correcto uso.', 12, false, 10);
    addIndentedText('F. Participar activamente en la prevención de riesgos de seguridad y salud en el trabajo mediante el reporte de actos o condiciones que ponga en riesgo la vida y/o salud propia y/o la de los demás trabajadores; además de los incidentes, accidentes y casi-accidentes que hayan ocurrido durante la ejecución del trabajo.', 12, false, 10);
    addIndentedText('G. Queda claramente establecido que dichas recomendaciones no exoneran a EL TRABAJADOR del conocimiento y cumplimiento de las demás normas y disposiciones de seguridad y salud en el trabajo que rijan en el centro de trabajo de EL EMPLEADOR, sean estas aplicables a su actividad especificas o dirigidas a la generalidad de trabajadores, tales como el reglamento interno de seguridad salud en el trabajo, los procedimientos de trabajo, las ordenes de trabajo, las instrucciones y recomendaciones emitidas en las capacitaciones impartidas, y cualquier otra sobre la materia.', 12, false, 10);

    // ENTREGA DE EQUIPOS
    addExtraSpace(2); 
    addText('ENTREGA DE EQUIPOS', 12, true, false, false);

    addText('CLÁUSULA QUINCE.', 12, true, false, false);
    addText('EL EMPLEADOR proporcionará al TRABAJADOR los materiales y equipos necesarios para el adecuado desarrollo de sus actividades. EL TRABAJADOR será responsable de los equipos y bienes de trabajo asignados los mismos que deben sufrir el desgaste propio y natural provocado por el uso normal. EL TRABAJADOR será responsable por los daños, pérdidas, extravíos o robos de los equipos y/o bienes de trabajo que se le hayan asignado. EL TRABAJADOR autoriza expresamente al empleador a deducir de su remuneración de su liquidación de sus beneficios sociales en caso de extinción de la relación laboral el costo de la reparación o reposición de los equipos de trabajo.', 12, false, false, true);

    // DE LA PROPIEDAD INTELECTUAL Y USO DE IMAGEN PARA FINES COMERCIALES Y PUBLICITARIOS
    addExtraSpace(2); 
    addText('DE LA PROPIEDAD INTELECTUAL Y USO DE IMAGEN PARA FINES COMERCIALES Y PUBLICITARIOS', 12, true, false, false);

    addText('CLÁUSULA DIECISÉIS.', 12, true, false, false);
    addText('EL TRABAJADOR acepta que todo producto, ya sea de naturaleza, física, digital o cualquiera sea su naturaleza, que se generen en el marco de sus labores según el presente contrato de trabajo, son de entera propiedad de EL EMPLEADOR pues se producen en el marco de la relación laboral. De igual forma, EL TRABAJADOR autoriza al uso de su imagen en toda producción audiovisual, fotográfica, de branding o cualquier otro tipo de publicidad, banners u otros similares, que produzca el EMPLEADOR donde figuren tanto él como otro trabajadores o directivos.', 12, false, false, true);

    // JURISDICCIÓN Y COMPETENCIA
    addExtraSpace(5); 
    addText('JURISDICCIÓN Y COMPETENCIA', 12, true, false, false);

    addText('CLÁUSULA DIECISIETE.', 12, true, false, false);
    addText(`Las partes contratantes renuncian expresamente al fuero judicial de sus domicilios y se someten a la jurisdicción de los jueces de la ciudad de ${this.contrato.empleador_sede} para resolver cualquier controversia que el cumplimiento del presente contrato pudiera originar. Conformes con todas las CLÁUSULAs del presente contrato, firman las partes por duplicado, el día ${this.contrato.fecha_firma}.`, 12, false, false, true);

    // Firmas - Mucho más espacio arriba
    addExtraSpace(20); // Agregar espacio extra manualmente
    addText('', 12, false, false, false); // Espacio
    addText('', 12, false, false, false); // Espacio
    addText('', 12, false, false, false); // Espacio
    addText('', 12, false, false, false); // Espacio
    addText('', 12, false, false, false); // Espacio
    addText('', 12, false, false, false); // Espacio

    // Firmas exactamente como en la imagen
    addText('', 12, false, false, false); // Espacio
    addText('', 12, false, false, false); // Espacio

    // Líneas de firma largas - CENTRADAS
    addText('_________________________________________                    _________________________________________', 12, false, true, false);

    // Nombres centrados debajo de cada línea - CENTRADOS
    addText(`   ${this.contrato.empleador_razon_social}                               ${this.contrato.empleado_nombres}`, 12, true, true, false);

    // Roles centrados - CENTRADOS
    addText(`                   EL EMPLEADOR                                                            DNI: ${this.contrato.empleado_dni} | EL TRABAJADOR`, 12, false, true, false);
  }

  // Función específica para generar PDF de contratos existentes usando la plantilla completa
  private async generatePdfFromTemplateExistente(fileName: string, contrato: Contrato): Promise<void> {
    console.log('Iniciando generatePdfFromTemplateExistente con fileName:', fileName);
    console.log('Datos del contrato:', contrato);

    try {
      // Temporalmente guardar los datos actuales del formulario
      const contratoOriginal = { ...this.contrato };

      // Cargar los datos del contrato existente en this.contrato para que la plantilla los use
      this.contrato = {
        empleador_razon_social: contrato.empleador_razon_social,
        empleador_ruc: contrato.empleador_ruc,
        empleador_domicilio: contrato.empleador_domicilio,
        empleador_sede: contrato.empleador_sede || 'CHICLAYO',
        empleador_departamento: contrato.empleador_departamento,
        empleador_provincia: contrato.empleador_provincia,
        empleador_distrito: contrato.empleador_distrito,
        empleado_nombres: contrato.empleado_nombres,
        empleado_dni: contrato.empleado_dni,
        empleado_domicilio: contrato.empleado_domicilio,
        empleado_departamento: contrato.empleado_departamento,
        empleado_provincia: contrato.empleado_provincia,
        empleado_distrito: contrato.empleado_distrito,
        empleado_cargo: contrato.empleado_cargo,
        empleado_cantidad_ventas: contrato.empleado_cantidad_ventas,
        fecha_inicio: contrato.fecha_inicio,
        fecha_fin: contrato.fecha_fin,
        sueldo: contrato.sueldo,
        numero_letras: contrato.numero_letras,
        importe: contrato.importe,
        importe_letras: contrato.importe_letras,
        email: contrato.email,
        empleado_telefono: contrato.empleado_telefono,
        categoria_contrato: contrato.categoria_contrato,
        fecha_firma: contrato.fecha_firma
      };

      // Usar la función generatePdfFromTemplate existente que ya tiene toda la plantilla completa
      await this.generatePdfFromTemplate(fileName);

      // Restaurar los datos originales del formulario
      this.contrato = contratoOriginal;

    } catch (error) {
      console.error('Error al generar PDF:', error);
      throw error;
    }
  }

  // ==================== MÉTODOS PARA SUBIR ARCHIVO ====================

  // Abrir modal para subir archivo
  abrirModalSubirArchivo(contrato: any): void {
    console.log('Abriendo modal para subir archivo del contrato:', contrato);
    this.contratoSeleccionado = contrato;
    this.showModalSubirArchivo = true;
    this.limpiarFormularioArchivo();
  }

  // Cerrar modal de subir archivo
  cerrarModalSubirArchivo(): void {
    // Usar setTimeout para evitar ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.showModalSubirArchivo = false;
      this.contratoSeleccionado = null;
      this.limpiarFormularioArchivo();
      this.cdr.detectChanges();
    }, 0);
  }

  // Limpiar formulario de archivo
  private limpiarFormularioArchivo(): void {
    this.archivoSeleccionado = null;
    this.errorArchivo = '';
    this.isSubiendoArchivo = false;
    this.isDragOver = false;
  }

  // Manejar selección de archivo
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    this.procesarArchivo(file);
  }

  // Manejar drag over
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  // Manejar drag leave
  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  // Manejar drop
  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.procesarArchivo(files[0]);
    }
  }

  // Procesar archivo seleccionado
  private procesarArchivo(file: File): void {
    this.errorArchivo = '';

    // Validar que sea un archivo
    if (!file) {
      this.errorArchivo = 'No se seleccionó ningún archivo';
      return;
    }

    // Validar tipo de archivo
    if (file.type !== 'application/pdf') {
      this.errorArchivo = 'Solo se permiten archivos PDF';
      return;
    }

    // Validar tamaño (10MB = 10 * 1024 * 1024 bytes)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      this.errorArchivo = 'El archivo no puede ser mayor a 10MB';
      return;
    }

    // Archivo válido
    this.archivoSeleccionado = file;
    console.log('Archivo seleccionado:', file.name, this.formatFileSize(file.size));
  }

  // Remover archivo seleccionado
  removerArchivo(): void {
    this.archivoSeleccionado = null;
    this.errorArchivo = '';
  }

  // Formatear tamaño de archivo
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Subir archivo
  async subirArchivo(): Promise<void> {
    if (!this.archivoSeleccionado || !this.contratoSeleccionado) {
      this.errorArchivo = 'Debe seleccionar un archivo';
      return;
    }

    this.isSubiendoArchivo = true;
    this.errorArchivo = '';
    this.cdr.detectChanges();

    try {
      // Generar nombre del archivo con formato: DNI_FECHA_HORA_NOMBRES.pdf
      const fechaHora = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const dni = this.contratoSeleccionado.empleado_dni || 'SIN_DNI';
      const nombres = this.contratoSeleccionado.empleado_nombres?.replace(/\s+/g, '_').toUpperCase() || 'SIN_NOMBRES';
      const nombreArchivo = `${dni}_${fechaHora}_${nombres}.pdf`;

      console.log('Subiendo archivo:', {
        archivoOriginal: this.archivoSeleccionado.name,
        nombreFinal: nombreArchivo,
        tamaño: this.formatFileSize(this.archivoSeleccionado.size),
        contrato: this.contratoSeleccionado
      });

      // Subir archivo real al servidor
      await this.subirArchivoReal(this.archivoSeleccionado, nombreArchivo);

      // Mostrar éxito
      this.notificationService.success(`Archivo subido exitosamente como: ${nombreArchivo}`);

      // Cerrar modal
      this.isSubiendoArchivo = false;
      this.cdr.detectChanges();

      setTimeout(() => {
        this.cerrarModalSubirArchivo();
      }, 500);

    } catch (error) {
      console.error('Error al subir archivo:', error);
      this.errorArchivo = 'Error al subir el archivo. Inténtelo nuevamente.';
      this.isSubiendoArchivo = false;
      this.cdr.detectChanges();
    }
  }

  // Guardar archivo en la carpeta public/uploads/contratos/ del proyecto
  private async subirArchivoReal(file: File, nombreArchivo: string): Promise<void> {
    try {
      console.log(`📁 Guardando archivo en: public/uploads/contratos/${nombreArchivo}`);
      console.log(`📄 Archivo original: ${file.name}`);
      console.log(`📊 Tamaño: ${this.formatFileSize(file.size)}`);

      // Crear FormData para enviar al servidor local
      const formData = new FormData();
      formData.append('file', file);
      formData.append('contrato_id', this.contratoSeleccionado.id?.toString() || '');
      formData.append('empleado_dni', this.contratoSeleccionado.empleado_dni || '');
      formData.append('empleado_nombres', this.contratoSeleccionado.empleado_nombres || '');

      console.log('📤 Enviando datos al servidor:', {
        nombreArchivo: nombreArchivo,
        empleado_dni: this.contratoSeleccionado.empleado_dni,
        empleado_nombres: this.contratoSeleccionado.empleado_nombres,
        archivoOriginal: file.name
      });

      // Enviar al servidor local que guarda en public/uploads/contratos/
      await new Promise((resolve, reject) => {
        this.http.post('http://localhost:3001/api/upload/contrato', formData).subscribe({
          next: (response: any) => {
            console.log(`✅ Archivo guardado exitosamente en: ${response.archivo.ruta}`);
            console.log('📂 Ubicación final: public/uploads/contratos/' + response.archivo.nombreFinal);

            // Actualizar la ruta del archivo en el backend
            if (this.contratoSeleccionado.id_contrato) {
              const rutaArchivo = `uploads/contratos/${response.archivo.nombreFinal}`;
              this.contratoService.actualizarRutaArchivoContrato(
                this.contratoSeleccionado.id_contrato,
                rutaArchivo
              ).subscribe({
                next: (updateResponse) => {
                  console.log(`📝 Ruta actualizada en BD: ${rutaArchivo}`);
                  console.log('✅ Respuesta del backend:', updateResponse);

                  // Recargar los contratos para mostrar la nueva ruta
                  console.log('🔄 Recargando contratos para mostrar nueva ruta...');
                  this.cargarContratos();
                },
                error: (error) => {
                  console.warn('⚠️ Error al actualizar ruta en BD:', error);
                  // Aún así recargar contratos por si acaso
                  this.cargarContratos();
                }
              });
            } else {
              // Si no hay ID de contrato, solo recargar
              this.cargarContratos();
            }

            resolve(response);
          },
          error: (error) => {
            console.error('❌ Error del servidor local:', error);

            if (error.status === 0) {
              reject(new Error('Servidor de uploads no está ejecutándose. Ejecuta: node upload-server.js'));
            } else {
              reject(new Error(`Error: ${error.error?.error || error.message}`));
            }
          }
        });
      });

    } catch (error) {
      console.error('❌ Error al subir archivo:', error);
      throw error;
    }
  }

  // ==================== MÉTODO PARA DESCARGAR ARCHIVO PDF ====================

  // Descargar archivo PDF del contrato usando la ruta guardada
  descargarArchivoPDF(contrato: any): void {
    if (!contrato.ruta_archivo) {
      this.notificationService.error('Este contrato no tiene archivo PDF asociado');
      return;
    }

    console.log('📥 Descargando archivo PDF:', {
      contrato_id: contrato.id_contrato,
      empleado: contrato.empleado_nombres,
      ruta_archivo: contrato.ruta_archivo
    });

    try {
      // Extraer solo el nombre del archivo de la ruta
      const nombreArchivo = this.extraerNombreArchivo(contrato.ruta_archivo);

      // Construir URL del servidor de uploads
      const urlDescarga = `http://localhost:3001/api/download/${nombreArchivo}`;

      console.log(`🔗 URL de descarga: ${urlDescarga}`);

      // Crear enlace de descarga
      const link = document.createElement('a');
      link.href = urlDescarga;
      link.download = nombreArchivo;
      link.target = '_blank';

      // Agregar al DOM, hacer click y remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`✅ Descarga iniciada: ${nombreArchivo}`);
      this.notificationService.success(`Descargando: ${nombreArchivo}`);

    } catch (error) {
      console.error('❌ Error al descargar archivo:', error);
      this.notificationService.error('Error al descargar el archivo PDF');
    }
  }

  // Extraer nombre del archivo de la ruta
  private extraerNombreArchivo(rutaArchivo: string): string {
    if (!rutaArchivo) return 'contrato.pdf';

    // Extraer solo el nombre del archivo de la ruta
    const partes = rutaArchivo.split('/');
    return partes[partes.length - 1] || 'contrato.pdf';
  }
}
