import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Personal {
  id_personal?: number | null;
  id_empresa?: number | null;
  primer_nombre?: string;
  segundo_nombre?: string;
  apellido_paterno?: string;
  apellido_materno?: string;
  tipo_documento?: string;
  documento?: string;
  apellidos_nombres_completos?: string;
  tipo_contrato?: string;
  categoria_contrato?: string;
  fecha_incorporacion?: string | null;
  fecha_cese?: string | null;
  fecha_nacimiento?: string | null;
  edad?: number;
  direccion?: string;
  departamento?: string;
  provincia?: string;
  distrito?: string;
  telefono_movil?: string;
  correo_electronico?: string;
  cargo?: string;
  sistema_pensiones?: string;
  nombre_institucion_educativa?: string;
  carrera_profesional?: string;
  institucion_bancaria?: string;
  cuenta_sueldo?: string;
  horario?: string;
  salario?: number;
  condicion_trabajo_alimentacion?: string;
  break_time?: string;
  asignacion_familiar?: string;
  importe?: number;
  motivo_cese?: string;
  importe_liquidacion?: number;
  genero?: string;
  coordinador?: string;
  tiempo_servicio?: string;
  inicio_contrato?: string | null;
  fin_contrato?: string | null;
  fecha_vencimiento_periodo_prueba?: string | null;
  sede?: string;
  digito_verificador?: string;
  respuesta_rit?: string;
  confidencialidad?: string;
  carga_familiar?: string;
  legalizacion_mapfre?: string;
  ruc?: string;
  razon_social?: string;
  direccion_empresa?: string;
  departamento_empresa?: string;
  provincia_empresa?: string;
  distrito_empresa?: string;
}


@Injectable({
  providedIn: 'root'
})
export class PersonalService {
  private apiUrl = 'http://localhost:8000/api/rrhh/personal/';

  constructor(private http: HttpClient) {}

  // GET: /personal/general/
  getPersonalGeneral(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}general/`);
  }

  // GET: /personal/{id_personal}/
  getPersonalById(id_personal: number): Observable<Personal> {
    return this.http.get<Personal>(`${this.apiUrl}${id_personal}/`);
  }

  // POST: /personal/crear/
  createPersonal(payload: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}crear/`, payload);
  }

  // PUT: /personal/actualizar/{id_personal}/
  updatePersonal(id_personal: number, payload: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}actualizar/${id_personal}/`, payload);
  }

  searchDni(dni: string): Observable<any> {
    const url = `https://api.factiliza.com/v1/dni/info/${dni}`;
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzODAyMCIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6ImNvbnN1bHRvciJ9.kIhnyl_BbKjGmMeuF4D9LN9g3-D6fcjofJH5vmeNeRg';
    const headers = { 'Authorization': `Bearer ${token}` };
    return this.http.get(url, { headers });
  }
}