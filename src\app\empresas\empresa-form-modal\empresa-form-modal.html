<div *ngIf="isVisible" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex justify-center items-center p-4">
  <div class="relative p-6 bg-white w-full max-w-xl mx-auto rounded-lg shadow-lg flex flex-col max-h-[80vh]">
    <!-- Head<PERSON> del Mo<PERSON> -->
    <div class="flex justify-between items-center pb-3 border-b border-gray-200">
      <h2 class="text-2xl font-bold">{{ isEditMode ? 'Editar Empresa' : 'Agregar Empresa' }}</h2>
      <button (click)="onClose()" class="text-gray-500 hover:text-gray-700 text-xl leading-none font-semibold">&times;</button>
    </div>

    <!-- Contenido del Formulario (con scroll) -->
    <div class="flex-grow overflow-y-auto py-4">

      <!-- Overlay tipo Sweet (versión más ancha) -->
      <div *ngIf="isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="flex flex-col items-center bg-white px-16 py-10 rounded-lg shadow-2xl w-[400px]">
          <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-500 border-solid mb-6"></div>
          <p class="text-gray-800 text-xl font-semibold text-center">Cargando, por favor espere...</p>
        </div>
      </div>

      <form [formGroup]="empresaForm" (ngSubmit)="saveForm()">
        <!-- Campos de Empresa -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="ruc" class="block text-gray-700 text-sm font-bold mb-2">RUC:</label>
            <div class="flex">
              <input type="text" id="ruc" formControlName="ruc" name="ruc" class="shadow appearance-none border rounded-l w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
              <button type="button" (click)="buscarRUC()" [disabled]="isSearchingRUC" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-3 rounded-r flex items-center justify-center">
                <svg *ngIf="!isSearchingRUC" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
                <span *ngIf="isSearchingRUC" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
              </button>
            </div>
          </div>
          <div>
            <label for="razon_social" class="block text-gray-700 text-sm font-bold mb-2">Razón Social:</label>
            <input type="text" id="razon_social" formControlName="razon_social" name="razon_social" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" />
          </div>
        </div>
        <div class="mb-4">
          <label for="direccion_empresa" class="block text-gray-700 text-sm font-bold mb-2">Dirección Empresa:</label>
          <input type="text" id="direccion_empresa" formControlName="direccion_empresa" name="direccion_empresa" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label for="departamento" class="block text-gray-700 text-sm font-bold mb-2">Departamento:</label>
            <select id="departamento" formControlName="departamento" (change)="onDepartamentoChange()" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option value="">Seleccione</option>
              <option *ngFor="let dep of departamentos" [value]="dep">{{dep}}</option>
            </select>
          </div>
          <div>
            <label for="provincia" class="block text-gray-700 text-sm font-bold mb-2">Provincia:</label>
            <select id="provincia" formControlName="provincia" (change)="onProvinciaChange()" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option value="">Seleccione</option>
              <option *ngFor="let prov of provincias" [value]="prov">{{prov}}</option>
            </select>
          </div>
          <div>
            <label for="distrito" class="block text-gray-700 text-sm font-bold mb-2">Distrito:</label>
            <select id="distrito" formControlName="distrito" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option value="">Seleccione</option>
              <option *ngFor="let dist of distritos" [value]="dist">{{dist}}</option>
            </select>
          </div>
        </div>

        <!-- Campos de Representante -->
        <h3 class="text-xl font-bold mb-4 mt-6">Datos del Representante</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="documento_representante" class="block text-gray-700 text-sm font-bold mb-2">DNI Representante:</label>
            <input type="text" id="documento_representante" formControlName="documento_representante" name="documento_representante" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
          </div>
          <div>
            <label for="nombres_representante" class="block text-gray-700 text-sm font-bold mb-2">Nombres Representante:</label>
            <input type="text" id="nombres_representante" formControlName="nombres_representante" name="nombres_representante" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label for="apellido_paterno_representante" class="block text-gray-700 text-sm font-bold mb-2">Apellido Paterno:</label>
              <input type="text" id="apellido_paterno_representante" formControlName="apellido_paterno_representante" name="apellido_paterno_representante" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>
            <div>
              <label for="apellido_materno_representante" class="block text-gray-700 text-sm font-bold mb-2">Apellido Materno:</label>
              <input type="text" id="apellido_materno_representante" formControlName="apellido_materno_representante" name="apellido_materno_representante" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>
          </div>
        <div class="mb-4">
          <label for="partida_registral" class="block text-gray-700 text-sm font-bold mb-2">Partida Registral:</label>
          <input type="text" id="partida_registral" formControlName="partida_registral" name="partida_registral" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
      </form>
    </div>

    <!-- Footer del Modal -->
    <div class="flex justify-end pt-3 border-t border-gray-200">
      <button type="button" (click)="onClose()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">Cancelar</button>
      <button type="submit" (click)="saveForm()" [disabled]="empresaForm.invalid || isLoading" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">Guardar</button>
    </div>
  </div>
</div>