import { Routes } from '@angular/router';
import { MainMenuComponent } from './main-menu/main-menu.component';
import { LoginComponent } from './login/login.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { DashboardPersonal } from './dashboard-personal/dashboard-personal';
import { PersonalComponent } from './personal/personal.component';
import { EmpresasComponent } from './empresas/empresas.component';
import { ContratosComponent } from './contratos/contratos.component';

export const routes: Routes = [
    { path: '', redirectTo: '/login', pathMatch: 'full' },
    { path: 'login', component: LoginComponent },
    { 
        path: 'main-menu', 
        component: MainMenuComponent,
        children: [
            { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
            { path: 'dashboard-personal', component: DashboardPersonal },
            { path: 'dashboard', component: DashboardComponent },
            { path: 'personal', component: PersonalComponent },
            { path: 'personal/:id', loadComponent: () => import('./personal/personal-details/personal-details.component').then(m => m.PersonalDetailsComponent) },
            { path: 'empresas', component: EmpresasComponent },
            { path: 'contratos', component: ContratosComponent }
        ]
    }
];