<div class="notification-container">
  <div *ngFor="let notification of notifications" class="notification" [ngClass]="'notification-' + notification.type" @fadeInOut>
    <div class="notification-icon" [ngClass]="getIconClass(notification.type)"></div>
    <div class="notification-message">{{ notification.message }}</div>
    <button class="notification-close" (click)="close(notification)">&times;</button>
  </div>
</div>
