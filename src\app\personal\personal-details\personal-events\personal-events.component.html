<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <h2 class="text-lg font-bold font-poppins text-custom-blue mb-4">Gestión de Eventos</h2>
  </div>

  <!-- Nuevo Evento -->
  <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
    <h3 class="text-lg font-bold font-poppins text-custom-blue mb-4">Nuevo Evento</h3>
    
    <form (ngSubmit)="guardarEvento()" class="space-y-4">
      <!-- Tipo de Evento y Estado -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="tipo_evento" class="block text-sm font-medium text-gray-700">
            Tipo de Evento <span class="text-red-500">*</span>
          </label>
          <select
            id="tipo_evento"
            [(ngModel)]="nuevoEvento.tipo_evento"
            name="tipo_evento"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
            <option value="CAPACITACION">Capacitación</option>
            <option value="REUNION">Reunión</option>
            <option value="EVALUACION">Evaluación</option>
            <option value="DISCIPLINARIO">Disciplinario</option>
            <option value="RECONOCIMIENTO">Reconocimiento</option>
            <option value="OTRO">Otro</option>
          </select>
        </div>

        <div>
          <label for="estado" class="block text-sm font-medium text-gray-700">
            Estado <span class="text-red-500">*</span>
          </label>
          <select
            id="estado"
            [(ngModel)]="nuevoEvento.estado"
            name="estado"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
            <option value="PROGRAMADO">Programado</option>
            <option value="EN_CURSO">En Curso</option>
            <option value="COMPLETADO">Completado</option>
            <option value="CANCELADO">Cancelado</option>
          </select>
        </div>
      </div>

      <!-- Título -->
      <div>
        <label for="titulo" class="block text-sm font-medium text-gray-700">
          Título <span class="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="titulo"
          [(ngModel)]="nuevoEvento.titulo"
          name="titulo"
          placeholder="Ej: Capacitación en Seguridad Laboral"
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
          required>
      </div>

      <!-- Descripción -->
      <div>
        <label for="descripcion" class="block text-sm font-medium text-gray-700">
          Descripción <span class="text-red-500">*</span>
        </label>
        <textarea
          id="descripcion"
          [(ngModel)]="nuevoEvento.descripcion"
          name="descripcion"
          rows="3"
          placeholder="Descripción detallada del evento..."
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm resize-none"
          required></textarea>
      </div>

      <!-- Fecha y Horarios -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="fecha_evento" class="block text-sm font-medium text-gray-700">
            Fecha del Evento <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="fecha_evento"
            [(ngModel)]="nuevoEvento.fecha_evento"
            name="fecha_evento"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>

        <div>
          <label for="hora_inicio" class="block text-sm font-medium text-gray-700">
            Hora de Inicio <span class="text-red-500">*</span>
          </label>
          <input
            type="time"
            id="hora_inicio"
            [(ngModel)]="nuevoEvento.hora_inicio"
            name="hora_inicio"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>

        <div>
          <label for="hora_fin" class="block text-sm font-medium text-gray-700">
            Hora de Fin <span class="text-red-500">*</span>
          </label>
          <input
            type="time"
            id="hora_fin"
            [(ngModel)]="nuevoEvento.hora_fin"
            name="hora_fin"
            class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            required>
        </div>
      </div>

      <!-- Ubicación -->
      <div>
        <label for="ubicacion" class="block text-sm font-medium text-gray-700">
          Ubicación
        </label>
        <input
          type="text"
          id="ubicacion"
          [(ngModel)]="nuevoEvento.ubicacion"
          name="ubicacion"
          placeholder="Ej: Sala de Conferencias A, Oficina de RRHH"
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
      </div>

      <!-- Observaciones -->
      <div>
        <label for="observaciones" class="block text-sm font-medium text-gray-700">
          Observaciones
        </label>
        <textarea
          id="observaciones"
          [(ngModel)]="nuevoEvento.observaciones"
          name="observaciones"
          rows="2"
          placeholder="Observaciones adicionales..."
          class="mt-1 block w-full px-2 py-1 rounded-md border border-indigo-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm resize-none"></textarea>
      </div>

      <!-- Botones -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          (click)="resetForm()"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Limpiar
        </button>
        <button
          type="submit"
          [disabled]="isSaving"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="!isSaving">Crear Evento</span>
          <span *ngIf="isSaving" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creando...
          </span>
        </button>
      </div>
    </form>
  </div>

  <!-- Historial de Eventos -->
  <div class="bg-white rounded-lg shadow-md border border-gray-200">
    <div class="p-6 border-b border-gray-200 bg-indigo-50">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-indigo-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-lg font-bold text-indigo-800">Historial de Eventos</h3>
        </div>
        
        <!-- Filtros -->
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <select
            [(ngModel)]="filtroTipo"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los tipos</option>
            <option value="CAPACITACION">Capacitación</option>
            <option value="REUNION">Reunión</option>
            <option value="EVALUACION">Evaluación</option>
            <option value="DISCIPLINARIO">Disciplinario</option>
            <option value="RECONOCIMIENTO">Reconocimiento</option>
            <option value="OTRO">Otro</option>
          </select>

          <select
            [(ngModel)]="filtroEstado"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los estados</option>
            <option value="PROGRAMADO">Programado</option>
            <option value="EN_CURSO">En Curso</option>
            <option value="COMPLETADO">Completado</option>
            <option value="CANCELADO">Cancelado</option>
          </select>

          <select
            [(ngModel)]="filtroAnio"
            (change)="aplicarFiltros()"
            class="px-2 py-1 border border-indigo-300 rounded-md text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Todos los años</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>

          <button
            (click)="limpiarFiltros()"
            class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 focus:outline-none">
            Limpiar filtros
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div *ngIf="isLoading" class="p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-600">Cargando eventos...</span>
      </div>
    </div>

    <!-- Tabla de eventos -->
    <div *ngIf="!isLoading" class="overflow-x-auto">
      <table class="min-w-full divide-y divide-indigo-200">
        <thead class="bg-gradient-to-r from-indigo-100 to-purple-100">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Evento</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Tipo</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Fecha y Hora</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Estado</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Ubicación</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngIf="displayedEventos.length === 0">
            <td colspan="5" class="px-6 py-8 text-center text-gray-500">
              <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-4m4-4h8m-4 8V7"/>
                </svg>
                <p class="text-gray-500">No se encontraron eventos</p>
              </div>
            </td>
          </tr>
          <tr *ngFor="let evento of displayedEventos; let i = index" 
              class="hover:bg-indigo-50 transition-colors duration-150"
              [class.bg-indigo-25]="i % 2 === 0">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8">
                  <div class="h-8 w-8 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <div class="font-medium text-gray-900">{{ evento.titulo }}</div>
                  <div class="text-gray-500 text-xs">{{ evento.descripcion | slice:0:50 }}{{ evento.descripcion.length > 50 ? '...' : '' }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getTipoClass(evento.tipo_evento)">
                {{ evento.tipo_evento }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                <div>
                  <div class="font-medium">{{ formatearFecha(evento.fecha_evento) }}</div>
                  <div class="text-gray-500 text-xs">{{ formatearHora(evento.hora_inicio) }} - {{ formatearHora(evento.hora_fin) }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getEstadoClass(evento.estado)">
                {{ evento.estado }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ evento.ubicacion || 'No especificada' }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div *ngIf="!isLoading && displayedEventos.length > 0" class="px-6 py-4 border-t border-gray-200">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700">Mostrar:</span>
          <select
            [(ngModel)]="itemsPerPage"
            (change)="onPageSizeChange(itemsPerPage)"
            class="border border-indigo-300 rounded px-2 py-1 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option [ngValue]="5">5</option>
            <option [ngValue]="10">10</option>
            <option [ngValue]="20">20</option>
          </select>
          <span class="text-sm text-gray-700">por página</span>
        </div>

        <div class="flex items-center space-x-2">
          <button
            (click)="goToPage(currentPage - 1)"
            [disabled]="currentPage === 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Anterior
          </button>

          <button
            *ngFor="let page of getPageNumbers()"
            (click)="goToPage(page)"
            [class.bg-blue-600]="page === currentPage"
            [class.text-white]="page === currentPage"
            [class.text-gray-700]="page !== currentPage"
            [class.hover:bg-gray-50]="page !== currentPage"
            class="px-3 py-1 border border-gray-300 rounded text-sm">
            {{ page }}
          </button>

          <button
            (click)="goToPage(currentPage + 1)"
            [disabled]="currentPage === totalPages"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Siguiente
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
