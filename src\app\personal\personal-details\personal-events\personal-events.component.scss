// Estilos específicos para el componente de eventos
.event-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.event-status {
  &.programado {
    @apply bg-blue-100 text-blue-800;
  }
  
  &.en-curso {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  &.completado {
    @apply bg-green-100 text-green-800;
  }
  
  &.cancelado {
    @apply bg-red-100 text-red-800;
  }
}

.event-type {
  &.capacitacion {
    @apply bg-purple-100 text-purple-800;
  }
  
  &.reunion {
    @apply bg-indigo-100 text-indigo-800;
  }
  
  &.evaluacion {
    @apply bg-orange-100 text-orange-800;
  }
  
  &.disciplinario {
    @apply bg-red-100 text-red-800;
  }
  
  &.reconocimiento {
    @apply bg-green-100 text-green-800;
  }
  
  &.otro {
    @apply bg-gray-100 text-gray-800;
  }
}

// Animaciones para loading
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Estilos para formularios
.form-section {
  @apply bg-gray-50 p-4 rounded-lg mb-4;
}

.required-field {
  @apply text-red-500;
}

// Responsive adjustments
@media (max-width: 768px) {
  .event-table {
    font-size: 0.875rem;
  }
  
  .event-filters {
    flex-direction: column;
    gap: 0.5rem;
  }
}
