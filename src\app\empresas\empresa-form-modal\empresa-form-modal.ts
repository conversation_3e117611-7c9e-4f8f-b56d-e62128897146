import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Empresa, EmpresaService } from '../../servicios/empresa.service';
import { UbigeoService } from '../../servicios/ubigeo.service';
import { NotificationService } from '../../servicios/notification.service';

@Component({
  selector: 'app-empresa-form-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './empresa-form-modal.html',
  styleUrls: ['./empresa-form-modal.scss']
})
export class EmpresaFormModal implements OnChanges {
  @Input() isVisible: boolean = false;
  @Input() isEditMode: boolean = false;
  @Input() empresaToEdit: Empresa | null = null;
  @Output() closeModal = new EventEmitter<void>();
  @Output() empresaGuardada = new EventEmitter<void>();

  empresaForm: FormGroup;
  departamentos: string[] = [];
  provincias: string[] = [];
  distritos: string[] = [];
  submitted = false;
  isLoading = false;
  isSearchingRUC = false;

  constructor(
    private fb: FormBuilder,
    private empresaService: EmpresaService,
    private ubigeoService: UbigeoService,
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService
  ) {
    this.empresaForm = this.fb.group({
      id_empresa: [0],
      ruc: ['', [Validators.required, Validators.pattern(/^\d{11}$/)]],
      razon_social: ['', Validators.required],
      direccion_empresa: [''],
      departamento: [''],
      provincia: [''],
      distrito: [''],
      tipo_documento_representante: ['DNI'],
      documento_representante: [''],
      nombres_representante: [''],
      apellido_paterno_representante: [''],
      apellido_materno_representante: [''],
      partida_registral: [''],
      estado_empresa: ['A', Validators.required]
    });
  }

  ngOnInit(): void {
    this.departamentos = this.ubigeoService.getDepartamentos();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.isVisible && this.isEditMode && this.empresaToEdit) {
      this.empresaForm.patchValue(this.empresaToEdit);
      // Lógica para cargar provincias y distritos si es necesario
    } else if (!this.isVisible) {
      this.resetForm();
    }
  }

  get f() { return this.empresaForm.controls; }

  onDepartamentoChange(): void {
    const departamento = this.empresaForm.get('departamento')?.value;
    this.provincias = this.ubigeoService.getProvincias(departamento);
    this.distritos = [];
    this.empresaForm.patchValue({ provincia: '', distrito: '' });
  }

  onProvinciaChange(): void {
    const departamento = this.empresaForm.get('departamento')?.value;
    const provincia = this.empresaForm.get('provincia')?.value;
    this.distritos = this.ubigeoService.getDistritos(departamento, provincia);
    this.empresaForm.patchValue({ distrito: '' });
  }

  buscarRUC(): void {
    const ruc = this.f.ruc.value;
    if (!ruc || this.f.ruc.invalid) {
      return;
    }

    this.isSearchingRUC = true;
    this.empresaService.searchEmpresaByRuc(ruc).subscribe({
      next: (data) => {
        if (data.success && data.data) {
          this.empresaForm.patchValue({
            razon_social: data.data.nombre_o_razon_social,
            direccion_empresa: data.data.direccion,
            departamento: data.data.departamento,
            provincia: data.data.provincia,
            distrito: data.data.distrito
          });
        }
        this.isSearchingRUC = false;
      },
      error: () => {
        this.isSearchingRUC = false;
        this.notificationService.error('No se pudo encontrar la empresa');
      }
    });
  }

  saveForm(): void {
    this.submitted = true;
    if (this.empresaForm.invalid) {
      return;
    }

    this.isLoading = true;
    const formData = this.empresaForm.value;

    const saveObservable = this.isEditMode
      ? this.empresaService.updateEmpresa(formData)
      : this.empresaService.createEmpresa(formData);

    saveObservable.subscribe({
      next: () => {
        this.isLoading = false;
        this.notificationService.success('Empresa guardada correctamente');
        this.closeModal.emit();
        this.empresaGuardada.emit();
      },
      error: (error) => {
        this.isLoading = false;
        this.notificationService.error(error.error.message || 'No se pudo guardar la empresa');
      }
    });
  }

  onClose(): void {
    this.closeModal.emit();
  }

  resetForm(): void {
    this.submitted = false;
    this.empresaForm.reset({
      id_empresa: 0,
      ruc: '',
      razon_social: '',
      direccion_empresa: '',
      departamento: '',
      provincia: '',
      distrito: '',
      tipo_documento_representante: 'DNI',
      documento_representante: '',
      nombres_representante: '',
      apellido_paterno_representante: '',
      apellido_materno_representante: '',
      partida_registral: '',
      estado_empresa: 'A'
    });
  }
}