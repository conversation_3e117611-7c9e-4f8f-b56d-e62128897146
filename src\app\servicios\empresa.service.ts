import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Empresa {
  id_empresa: number;
  ruc: string;
  razon_social: string;
  nombre_comercial: string | null;
  direccion_empresa: string;
  departamento: string;
  provincia: string;
  distrito: string;
  telefono_empresa: string | null;
  correo_empresa: string | null;
  estado_empresa: string;
  id_representante: number;
  dni: string;
  nombres: string;
  apellidos: string;
  direccion_representante: string | null;
  telefono_representante: string | null;
  correo_representante: string | null;
  cargo: string;
  fecha_cargo: string;
  tipo_representante: string;
}

@Injectable({
  providedIn: 'root'
})
export class EmpresaService {
  private apiUrl = 'http://localhost:8000/api/rrhh/empresa/';
  private factilizaApiUrl = 'https://api.factiliza.com/v1/ruc/';
  private authToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzODAyMCIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6ImNvbnN1bHRvciJ9.kIhnyl_BbKjGmMeuF4D9LN9g3-D6fcjofJH5vmeNeRg';

  constructor(private http: HttpClient) { }

  getEmpresas(): Observable<Empresa[]> {
    return this.http.get<Empresa[]>(this.apiUrl);
  }

  createEmpresa(empresa: any): Observable<any> {
    return this.http.post<any>(this.apiUrl, empresa);
  }

  updateEmpresa(empresa: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}${empresa.id_empresa}/`, empresa);
  }

  getEmpresaById(id: number): Observable<Empresa> {
    return this.http.get<Empresa>(`${this.apiUrl}${id}/`);
  }

  searchEmpresaByRuc(ruc: string): Observable<any> {
    const headers = { 'Authorization': `Bearer ${this.authToken}` };
    return this.http.get<any>(`${this.factilizaApiUrl}info/${ruc}`, { headers });
  }

  searchRepresentanteByRuc(ruc: string): Observable<any> {
    const headers = { 'Authorization': `Bearer ${this.authToken}` };
    return this.http.get<any>(`${this.factilizaApiUrl}representante/${ruc}`, { headers });
  }
}