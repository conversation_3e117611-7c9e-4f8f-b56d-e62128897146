-- =====================================================
-- Script de creación de tabla para Gestión de Vacaciones
-- Sistema de Recursos Humanos - MIDAS SOLUTIONS CENTER
-- =====================================================

-- Crear base de datos si no existe
-- CREATE DATABASE IF NOT EXISTS rrhh_midas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE rrhh_midas;

-- =====================================================
-- Tabla: vacaciones
-- Descripción: Almacena las solicitudes de vacaciones del personal
-- =====================================================

CREATE TABLE IF NOT EXISTS vacaciones (
    -- Clave primaria
    id_vacacion INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con tabla personal
    id_personal INT NOT NULL,
    
    -- Fechas de vacaciones
    fecha_inicio DATE NOT NULL COMMENT 'Fecha de inicio de las vacaciones',
    fecha_fin DATE NOT NULL COMMENT 'Fecha de fin de las vacaciones',
    
    -- Información de días
    dias_solicitados INT NOT NULL COMMENT 'Número de días solicitados',
    dias_disponibles INT NOT NULL DEFAULT 30 COMMENT 'Días disponibles del empleado al momento de la solicitud',
    
    -- Estado de la solicitud
    estado ENUM('PENDIENTE', 'APROBADO', 'RECHAZADO') NOT NULL DEFAULT 'PENDIENTE' COMMENT 'Estado actual de la solicitud',
    
    -- Información de la solicitud
    motivo VARCHAR(500) NOT NULL COMMENT 'Motivo de la solicitud de vacaciones',
    observaciones TEXT NULL COMMENT 'Observaciones adicionales del empleado',
    
    -- Fechas de gestión
    fecha_solicitud DATE NOT NULL COMMENT 'Fecha en que se realizó la solicitud',
    fecha_respuesta DATETIME NULL COMMENT 'Fecha en que se aprobó o rechazó la solicitud',
    
    -- Información de aprobación
    aprobado_por VARCHAR(255) NULL COMMENT 'Nombre de quien aprobó o rechazó la solicitud',
    id_aprobador INT NULL COMMENT 'ID del usuario que aprobó o rechazó',
    motivo_rechazo TEXT NULL COMMENT 'Motivo del rechazo si aplica',
    
    -- Campos de auditoría
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación del registro',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    
    -- Índices
    INDEX idx_personal (id_personal),
    INDEX idx_estado (estado),
    INDEX idx_fecha_inicio (fecha_inicio),
    INDEX idx_fecha_solicitud (fecha_solicitud),
    INDEX idx_created_at (created_at),
    
    -- Restricciones
    CONSTRAINT chk_fechas CHECK (fecha_fin >= fecha_inicio),
    CONSTRAINT chk_dias_positivos CHECK (dias_solicitados > 0),
    CONSTRAINT chk_dias_disponibles CHECK (dias_disponibles >= 0),
    
    -- Clave foránea (descomenta si tienes la tabla personal)
    -- FOREIGN KEY (id_personal) REFERENCES personal(id_personal) ON DELETE CASCADE ON UPDATE CASCADE,
    -- FOREIGN KEY (id_aprobador) REFERENCES usuarios(id_usuario) ON DELETE SET NULL ON UPDATE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabla para gestión de solicitudes de vacaciones';

-- =====================================================
-- Tabla: configuracion_vacaciones
-- Descripción: Configuración general de políticas de vacaciones
-- =====================================================

CREATE TABLE IF NOT EXISTS configuracion_vacaciones (
    id_config INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Configuración general
    dias_anuales_default INT NOT NULL DEFAULT 30 COMMENT 'Días de vacaciones por defecto al año',
    dias_minimos_solicitud INT NOT NULL DEFAULT 1 COMMENT 'Mínimo de días que se pueden solicitar',
    dias_maximos_solicitud INT NOT NULL DEFAULT 30 COMMENT 'Máximo de días que se pueden solicitar de una vez',
    
    -- Configuración de fechas
    dias_anticipacion_minima INT NOT NULL DEFAULT 15 COMMENT 'Días mínimos de anticipación para solicitar',
    permite_fines_semana BOOLEAN DEFAULT TRUE COMMENT 'Si se permiten vacaciones en fines de semana',
    permite_festivos BOOLEAN DEFAULT TRUE COMMENT 'Si se permiten vacaciones en días festivos',
    
    -- Configuración de aprobación
    requiere_aprobacion BOOLEAN DEFAULT TRUE COMMENT 'Si las solicitudes requieren aprobación',
    auto_aprobar_hasta_dias INT NULL COMMENT 'Auto-aprobar solicitudes hasta X días (NULL = siempre requiere aprobación)',
    
    -- Configuración por empresa/departamento
    id_empresa INT NULL COMMENT 'ID de empresa (NULL = configuración global)',
    departamento VARCHAR(100) NULL COMMENT 'Departamento específico (NULL = todos)',
    
    -- Auditoría
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_empresa (id_empresa),
    INDEX idx_departamento (departamento),
    INDEX idx_activo (activo)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Configuración de políticas de vacaciones';

-- =====================================================
-- Tabla: saldo_vacaciones
-- Descripción: Control del saldo de días de vacaciones por empleado
-- =====================================================

CREATE TABLE IF NOT EXISTS saldo_vacaciones (
    id_saldo INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Relación con empleado
    id_personal INT NOT NULL,
    
    -- Período
    anio INT NOT NULL COMMENT 'Año del período de vacaciones',
    
    -- Saldos
    dias_asignados INT NOT NULL DEFAULT 30 COMMENT 'Días asignados para el año',
    dias_utilizados INT NOT NULL DEFAULT 0 COMMENT 'Días ya utilizados',
    dias_pendientes INT NOT NULL DEFAULT 0 COMMENT 'Días en solicitudes pendientes',
    dias_disponibles INT GENERATED ALWAYS AS (dias_asignados - dias_utilizados - dias_pendientes) STORED COMMENT 'Días disponibles (calculado)',
    
    -- Información adicional
    dias_vencidos INT NOT NULL DEFAULT 0 COMMENT 'Días que vencieron sin usar',
    fecha_vencimiento DATE NULL COMMENT 'Fecha límite para usar los días',
    
    -- Auditoría
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    UNIQUE KEY uk_personal_anio (id_personal, anio),
    INDEX idx_anio (anio),
    INDEX idx_disponibles (dias_disponibles),
    
    -- Restricciones
    CONSTRAINT chk_saldo_dias_positivos CHECK (dias_asignados >= 0 AND dias_utilizados >= 0 AND dias_pendientes >= 0),
    CONSTRAINT chk_saldo_coherente CHECK (dias_utilizados + dias_pendientes <= dias_asignados)
    
    -- Clave foránea
    -- FOREIGN KEY (id_personal) REFERENCES personal(id_personal) ON DELETE CASCADE ON UPDATE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Control de saldo de días de vacaciones por empleado';

-- =====================================================
-- Datos de ejemplo para pruebas
-- =====================================================

-- Insertar configuración por defecto
INSERT INTO configuracion_vacaciones (
    dias_anuales_default, 
    dias_minimos_solicitud, 
    dias_maximos_solicitud, 
    dias_anticipacion_minima,
    requiere_aprobacion
) VALUES (30, 1, 15, 15, TRUE);

-- Insertar saldos de ejemplo (asumiendo que existe personal con id 8)
INSERT INTO saldo_vacaciones (id_personal, anio, dias_asignados, dias_utilizados, dias_pendientes) 
VALUES 
(8, 2024, 30, 15, 0),
(8, 2023, 30, 30, 0);

-- Insertar vacaciones de ejemplo
INSERT INTO vacaciones (
    id_personal, 
    fecha_inicio, 
    fecha_fin, 
    dias_solicitados, 
    dias_disponibles, 
    estado, 
    motivo, 
    fecha_solicitud, 
    fecha_respuesta, 
    aprobado_por
) VALUES 
(8, '2024-12-15', '2024-12-29', 15, 30, 'APROBADO', 'Vacaciones de fin de año', '2024-11-15', '2024-11-16 14:30:00', 'Gerente RRHH'),
(8, '2024-07-01', '2024-07-15', 15, 30, 'APROBADO', 'Vacaciones de medio año', '2024-06-01', '2024-06-02 11:00:00', 'Gerente RRHH'),
(8, '2025-03-15', '2025-03-22', 8, 30, 'PENDIENTE', 'Vacaciones familiares', '2025-01-15', NULL, NULL);

-- =====================================================
-- Vistas útiles
-- =====================================================

-- Vista para consultar vacaciones con información completa
CREATE OR REPLACE VIEW vista_vacaciones_completa AS
SELECT 
    v.id_vacacion,
    v.id_personal,
    -- p.nombres, p.apellidos, -- Descomenta si tienes tabla personal
    v.fecha_inicio,
    v.fecha_fin,
    v.dias_solicitados,
    v.estado,
    v.motivo,
    v.observaciones,
    v.fecha_solicitud,
    v.fecha_respuesta,
    v.aprobado_por,
    v.motivo_rechazo,
    DATEDIFF(v.fecha_fin, v.fecha_inicio) + 1 AS dias_calculados,
    CASE 
        WHEN v.estado = 'PENDIENTE' THEN 'Esperando aprobación'
        WHEN v.estado = 'APROBADO' THEN 'Vacaciones aprobadas'
        WHEN v.estado = 'RECHAZADO' THEN 'Solicitud rechazada'
    END AS estado_descripcion,
    v.created_at,
    v.updated_at
FROM vacaciones v
-- LEFT JOIN personal p ON v.id_personal = p.id_personal -- Descomenta si tienes tabla personal
ORDER BY v.created_at DESC;

-- Vista para resumen de vacaciones por empleado
CREATE OR REPLACE VIEW vista_resumen_vacaciones AS
SELECT 
    sv.id_personal,
    sv.anio,
    sv.dias_asignados,
    sv.dias_utilizados,
    sv.dias_pendientes,
    sv.dias_disponibles,
    COUNT(v.id_vacacion) as total_solicitudes,
    SUM(CASE WHEN v.estado = 'APROBADO' THEN 1 ELSE 0 END) as solicitudes_aprobadas,
    SUM(CASE WHEN v.estado = 'PENDIENTE' THEN 1 ELSE 0 END) as solicitudes_pendientes,
    SUM(CASE WHEN v.estado = 'RECHAZADO' THEN 1 ELSE 0 END) as solicitudes_rechazadas
FROM saldo_vacaciones sv
LEFT JOIN vacaciones v ON sv.id_personal = v.id_personal AND YEAR(v.fecha_inicio) = sv.anio
GROUP BY sv.id_personal, sv.anio
ORDER BY sv.anio DESC, sv.id_personal;

-- =====================================================
-- Procedimientos almacenados útiles
-- =====================================================

DELIMITER //

-- Procedimiento para aprobar una solicitud de vacaciones
CREATE PROCEDURE AprobarVacaciones(
    IN p_id_vacacion INT,
    IN p_aprobado_por VARCHAR(255),
    IN p_id_aprobador INT
)
BEGIN
    DECLARE v_id_personal INT;
    DECLARE v_anio INT;
    DECLARE v_dias_solicitados INT;
    
    -- Obtener datos de la solicitud
    SELECT id_personal, YEAR(fecha_inicio), dias_solicitados 
    INTO v_id_personal, v_anio, v_dias_solicitados
    FROM vacaciones 
    WHERE id_vacacion = p_id_vacacion AND estado = 'PENDIENTE';
    
    IF v_id_personal IS NOT NULL THEN
        -- Actualizar la solicitud
        UPDATE vacaciones 
        SET estado = 'APROBADO',
            fecha_respuesta = NOW(),
            aprobado_por = p_aprobado_por,
            id_aprobador = p_id_aprobador
        WHERE id_vacacion = p_id_vacacion;
        
        -- Actualizar saldo de vacaciones
        UPDATE saldo_vacaciones 
        SET dias_utilizados = dias_utilizados + v_dias_solicitados,
            dias_pendientes = dias_pendientes - v_dias_solicitados
        WHERE id_personal = v_id_personal AND anio = v_anio;
        
        SELECT 'Vacaciones aprobadas correctamente' as mensaje;
    ELSE
        SELECT 'Solicitud no encontrada o ya procesada' as mensaje;
    END IF;
END //

-- Procedimiento para rechazar una solicitud de vacaciones
CREATE PROCEDURE RechazarVacaciones(
    IN p_id_vacacion INT,
    IN p_aprobado_por VARCHAR(255),
    IN p_id_aprobador INT,
    IN p_motivo_rechazo TEXT
)
BEGIN
    DECLARE v_id_personal INT;
    DECLARE v_anio INT;
    DECLARE v_dias_solicitados INT;
    
    -- Obtener datos de la solicitud
    SELECT id_personal, YEAR(fecha_inicio), dias_solicitados 
    INTO v_id_personal, v_anio, v_dias_solicitados
    FROM vacaciones 
    WHERE id_vacacion = p_id_vacacion AND estado = 'PENDIENTE';
    
    IF v_id_personal IS NOT NULL THEN
        -- Actualizar la solicitud
        UPDATE vacaciones 
        SET estado = 'RECHAZADO',
            fecha_respuesta = NOW(),
            aprobado_por = p_aprobado_por,
            id_aprobador = p_id_aprobador,
            motivo_rechazo = p_motivo_rechazo
        WHERE id_vacacion = p_id_vacacion;
        
        -- Liberar días pendientes
        UPDATE saldo_vacaciones 
        SET dias_pendientes = dias_pendientes - v_dias_solicitados
        WHERE id_personal = v_id_personal AND anio = v_anio;
        
        SELECT 'Vacaciones rechazadas correctamente' as mensaje;
    ELSE
        SELECT 'Solicitud no encontrada o ya procesada' as mensaje;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- Triggers para mantener consistencia
-- =====================================================

DELIMITER //

-- Trigger para actualizar saldo al crear solicitud
CREATE TRIGGER tr_vacaciones_insert 
AFTER INSERT ON vacaciones
FOR EACH ROW
BEGIN
    IF NEW.estado = 'PENDIENTE' THEN
        -- Actualizar días pendientes
        INSERT INTO saldo_vacaciones (id_personal, anio, dias_asignados, dias_pendientes)
        VALUES (NEW.id_personal, YEAR(NEW.fecha_inicio), 30, NEW.dias_solicitados)
        ON DUPLICATE KEY UPDATE 
        dias_pendientes = dias_pendientes + NEW.dias_solicitados;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- Índices adicionales para optimización
-- =====================================================

-- Índices compuestos para consultas frecuentes
CREATE INDEX idx_vacaciones_personal_estado ON vacaciones(id_personal, estado);
CREATE INDEX idx_vacaciones_fechas ON vacaciones(fecha_inicio, fecha_fin);
CREATE INDEX idx_vacaciones_anio ON vacaciones(id_personal, fecha_inicio);

-- =====================================================
-- Comentarios finales
-- =====================================================

/*
NOTAS DE IMPLEMENTACIÓN:

1. Descomenta las claves foráneas cuando tengas las tablas relacionadas (personal, usuarios)
2. Ajusta los valores por defecto según las políticas de tu empresa
3. Los triggers y procedimientos son opcionales pero recomendados para mantener consistencia
4. Las vistas facilitan las consultas desde la aplicación
5. Considera agregar más validaciones según tus necesidades específicas

CONSULTAS ÚTILES:

-- Ver todas las vacaciones de un empleado
SELECT * FROM vista_vacaciones_completa WHERE id_personal = 8;

-- Ver resumen de vacaciones por año
SELECT * FROM vista_resumen_vacaciones WHERE id_personal = 8;

-- Ver solicitudes pendientes
SELECT * FROM vacaciones WHERE estado = 'PENDIENTE' ORDER BY fecha_solicitud;

-- Calcular días disponibles
SELECT id_personal, dias_disponibles FROM saldo_vacaciones WHERE anio = YEAR(CURDATE());
*/
