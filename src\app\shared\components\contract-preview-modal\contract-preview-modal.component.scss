.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    width: 90%; /* Adjust as needed */
    max-width: 800px; /* Max width for large modal */
    max-height: 90%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
  }
  
  .modal-title {
    font-size: 1.5em;
    font-weight: bold;
  }
  
  .modal-close-button {
    background: none;
    border: none;
    font-size: 1.8em;
    cursor: pointer;
    color: #aaa;
  }
  
  .modal-close-button:hover {
    color: #333;
  }
  
  .modal-body {
    flex-grow: 1;
    overflow-y: auto;
    margin-bottom: 15px;
  }
  
  .modal-footer {
    border-top: 1px solid #eee;
    padding-top: 10px;
    text-align: right;
  }
  
  .modal-button {
    background-color: #4CAF50; /* Green */
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
  }
  
  .modal-button:hover {
    background-color: #45a049;
  }
  