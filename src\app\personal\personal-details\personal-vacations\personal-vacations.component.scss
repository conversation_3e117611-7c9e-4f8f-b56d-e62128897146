// Estilos para el componente de vacaciones
.vacation-card {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// Estilos para radio buttons personalizados
.radio-option {
  @apply relative flex items-center px-6 py-3 rounded-lg border-2 cursor-pointer;
  @apply transition-all duration-200 hover:shadow-md;

  &.selected {
    &.gozadas {
      @apply border-green-500 bg-green-50 text-green-700 shadow-sm;

      .radio-circle {
        @apply border-green-500 bg-green-500;
      }
    }

    &.compradas {
      @apply border-blue-500 bg-blue-50 text-blue-700 shadow-sm;

      .radio-circle {
        @apply border-blue-500 bg-blue-500;
      }
    }
  }

  &:not(.selected) {
    @apply border-gray-300 bg-white text-gray-700 hover:border-gray-400;

    .radio-circle {
      @apply border-gray-300;
    }
  }

  .radio-circle {
    @apply w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center;
    @apply transition-all duration-200;

    .radio-dot {
      @apply w-2 h-2 rounded-full bg-white;
    }
  }

  .radio-icon {
    @apply w-5 h-5 mr-2;
    @apply transition-colors duration-200;
  }

  .radio-label {
    @apply font-medium;
  }
}

// Animaciones para los radio buttons
@keyframes radioSelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.radio-circle.selected {
  animation: radioSelect 0.3s ease-in-out;
}

// Estilos para los estados de vacaciones
.estado-badge {
  &.aprobado {
    @apply bg-green-100 text-green-800 border border-green-200;
  }
  
  &.rechazado {
    @apply bg-red-100 text-red-800 border border-red-200;
  }
  
  &.pendiente {
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
  }
}

// Animaciones para loading
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Estilos para formularios
.form-group {
  @apply mb-4;
  
  label {
    @apply block text-sm font-medium text-gray-700 mb-1;
    
    .required {
      @apply text-red-500;
    }
  }
  
  input, textarea, select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
    
    &:disabled, &[readonly] {
      @apply bg-gray-50 text-gray-600 cursor-not-allowed;
    }
    
    &.error {
      @apply border-red-500 focus:ring-red-500;
    }
  }
}

// Estilos para botones
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  
  &.btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
    
    &:disabled {
      @apply bg-blue-400 cursor-not-allowed;
    }
  }
  
  &.btn-secondary {
    @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  &.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  &.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
}

// Estilos optimizados para las tablas de vacaciones
.vacation-table {
  th {
    @apply px-6 py-3 text-left text-xs font-medium uppercase tracking-wider;
    
    &.gozadas { @apply bg-gradient-to-r from-green-100 to-emerald-100 text-green-700; }
    &.compradas { @apply bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700; }
  }
  
  td { @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900; }
  
  tr {
    @apply transition-colors duration-150;
    
    &.gozadas {
      @apply hover:bg-green-50;
      &:nth-child(even) { @apply bg-green-50; }
    }
    
    &.compradas {
      @apply hover:bg-blue-50;
      &:nth-child(even) { @apply bg-blue-50; }
    }
  }
}

.vacation-avatar {
  @apply h-8 w-8 rounded-full flex items-center justify-center;
  
  &.gozadas { @apply bg-gradient-to-r from-green-500 to-emerald-500; }
  &.compradas { @apply bg-gradient-to-r from-blue-500 to-cyan-500; }
  
  .avatar-icon { @apply w-4 h-4 text-white; }
}

.vacation-info-icon {
  @apply w-4 h-4 mr-2;
  
  &.dias { @apply text-green-500; }
  &.precio { @apply text-green-500; }
  &.total { @apply text-green-600; }
  &.motivo { @apply text-indigo-500; }
  &.fecha { @apply text-gray-400; }
}

// Estilos para paginación
.pagination {
  button {
    @apply px-3 py-1 border border-gray-300 rounded text-sm;
    @apply hover:bg-gray-50 transition-colors duration-150;
    
    &:disabled {
      @apply opacity-50 cursor-not-allowed hover:bg-white;
    }
    
    &.active {
      @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
    }
  }
}

// Estilos para filtros
.filters {
  select {
    @apply px-3 py-2 border border-gray-300 rounded-md text-sm;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
  }
}

// Estilos responsivos
@media (max-width: 640px) {
  .vacation-table {
    th, td {
      @apply px-3 py-2;
    }
  }
  
  .filters {
    @apply flex-col space-y-2;
    
    select {
      @apply w-full;
    }
  }
}

// Estilos para mensajes vacíos
.empty-state {
  @apply text-center py-12;
  
  .icon {
    @apply mx-auto h-12 w-12 text-gray-400 mb-4;
  }
  
  .title {
    @apply text-lg font-medium text-gray-900 mb-2;
  }
  
  .description {
    @apply text-gray-500;
  }
}

// Estilos para tooltips
.tooltip {
  @apply relative;
  
  &:hover .tooltip-content {
    @apply opacity-100 visible;
  }
  
  .tooltip-content {
    @apply absolute z-10 px-2 py-1 text-xs text-white bg-gray-900 rounded;
    @apply opacity-0 invisible transition-opacity duration-200;
    @apply -top-8 left-1/2 transform -translate-x-1/2;
    
    &::after {
      content: '';
      @apply absolute top-full left-1/2 transform -translate-x-1/2;
      @apply border-4 border-transparent border-t-gray-900;
    }
  }
}
