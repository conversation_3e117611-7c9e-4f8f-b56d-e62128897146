{"name": "recursos-humanos", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:recursos-humanos": "node dist/recursos-humanos/server/server.mjs", "upload-server": "node upload-server.js", "dev": "concurrently \"npm run start\" \"npm run upload-server\""}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "^20.1.4", "@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/platform-server": "^20.1.0", "@angular/router": "^20.1.0", "@angular/ssr": "^20.1.4", "cors": "^2.8.5", "express": "^5.1.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "multer": "^2.0.2", "rxjs": "~7.8.0", "sweetalert2": "^11.22.4", "tailwindcss": "^3.0.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.1.4", "@angular/cli": "^20.1.4", "@angular/compiler-cli": "^20.1.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "autoprefixer": "^10.4.21", "concurrently": "^9.2.1", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.6", "tailwindcss-cli": "^0.1.2", "typescript": "~5.8.2"}}