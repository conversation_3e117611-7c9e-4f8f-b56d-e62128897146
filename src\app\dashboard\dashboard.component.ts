import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { VacacionesService, DashboardVacaciones, PersonalVacacion } from '../services/vacaciones.service';
import { NotificationService } from '../servicios/notification.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {

  // Propiedades para el dashboard de vacaciones
  dashboardData: DashboardVacaciones | null = null;
  anioSeleccionado: string = '2025';
  isLoading: boolean = false;

  // Propiedades para paginación
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 1;
  displayedPersonal: PersonalVacacion[] = [];

  // Propiedades para filtros
  filtroSede: string = '';
  filtroCorresponde: string = '';

  // Referencia a Math para usar en el template
  Math = Math;

  constructor(
    private vacacionesService: VacacionesService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  // Cargar datos del dashboard
  loadDashboardData(): void {
    this.isLoading = true;
    const anio = parseInt(this.anioSeleccionado);

    console.log(`🔄 Cargando dashboard de vacaciones para el año ${anio}`);

    this.vacacionesService.obtenerListadoPersonalVacaciones(anio)
      .subscribe({
        next: (data) => {
          console.log('✅ Datos del dashboard recibidos:', data);
          this.dashboardData = data;
          this.updateDisplayedPersonal();
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('❌ Error al cargar dashboard:', error);
          this.notificationService.error(`Error al cargar dashboard: ${error.message}`);
          this.dashboardData = null;
          this.displayedPersonal = [];
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Cambio de año seleccionado
  onAnioChange(): void {
    console.log('Año seleccionado:', this.anioSeleccionado);
    this.loadDashboardData();
    this.notificationService.info(`Mostrando datos de vacaciones para el año ${this.anioSeleccionado}`);
  }

  // Actualizar personal mostrado con filtros y paginación
  updateDisplayedPersonal(): void {
    if (!this.dashboardData) {
      this.displayedPersonal = [];
      return;
    }

    let filteredPersonal = [...this.dashboardData.personal];

    // Aplicar filtros
    if (this.filtroSede) {
      filteredPersonal = filteredPersonal.filter(p => p.sede === this.filtroSede);
    }

    if (this.filtroCorresponde) {
      filteredPersonal = filteredPersonal.filter(p => p.vacaciones_de_ley === this.filtroCorresponde);
    }

    // Calcular paginación
    this.totalPages = Math.ceil(filteredPersonal.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.displayedPersonal = filteredPersonal.slice(startIndex, endIndex);
  }

  // Aplicar filtros
  aplicarFiltros(): void {
    this.currentPage = 1;
    this.updateDisplayedPersonal();
  }

  // Limpiar filtros
  limpiarFiltros(): void {
    this.filtroSede = '';
    this.filtroCorresponde = '';
    this.currentPage = 1;
    this.updateDisplayedPersonal();
  }

  // Métodos de paginación
  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updateDisplayedPersonal();
    }
  }

  onPageSizeChange(newSize: number): void {
    this.itemsPerPage = newSize;
    this.currentPage = 1;
    this.updateDisplayedPersonal();
  }

  getPageNumbers(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  // Obtener sedes únicas para el filtro
  getSedesUnicas(): string[] {
    if (!this.dashboardData) return [];
    return Object.keys(this.dashboardData.totales_por_sede);
  }

  // Obtener keys de totales por sede
  getSedeKeys(): string[] {
    if (!this.dashboardData) return [];
    return Object.keys(this.dashboardData.totales_por_sede);
  }
}
