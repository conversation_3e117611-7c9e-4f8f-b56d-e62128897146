import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Contrato {
  id_contrato?: number;
  id_personal: number;
  empleador_razon_social: string;
  empleador_ruc: string;
  empleador_domicilio: string;
  empleador_sede: string;
  empleador_departamento: string;
  empleador_provincia: string;
  empleador_distrito: string;
  empleado_nombres: string;
  empleado_dni: string;
  empleado_domicilio: string;
  empleado_departamento: string;
  empleado_provincia: string;
  empleado_distrito: string;
  empleado_cargo: string;
  empleado_cantidad_ventas?: number;
  fecha_inicio: string;
  fecha_fin: string;
  sueldo: number;
  numero_letras: string;
  importe: number;
  importe_letras: string;
  email: string;
  empleado_telefono: string;
  categoria_contrato: string;
  genero: string;
  fecha_firma: string;
  fecha_creacion?: string;
  fecha_actualizacion?: string;
  ruta_archivo?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ContratoService {
  private apiUrl = `${environment.apiUrl}/contrato`;

  constructor(private http: HttpClient) { }

  private getHttpOptions() {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };
  }

  // Listar contratos por personal
  listarContratosPorPersonal(idPersonal: number): Observable<Contrato[]> {
    return this.http.get<Contrato[]>(`${this.apiUrl}/personal/${idPersonal}/`, this.getHttpOptions());
  }

  // Obtener contrato por ID
  obtenerContratoPorId(idContrato: number): Observable<Contrato> {
    return this.http.get<Contrato>(`${this.apiUrl}/${idContrato}/`, this.getHttpOptions());
  }

  // Crear contrato
  crearContrato(contrato: Contrato): Observable<Contrato> {
    return this.http.post<Contrato>(`${this.apiUrl}/crear/`, contrato, this.getHttpOptions());
  }

  // Actualizar contrato
  actualizarContrato(idContrato: number, contrato: Contrato): Observable<Contrato> {
    return this.http.put<Contrato>(`${this.apiUrl}/actualizar/${idContrato}/`, contrato, this.getHttpOptions());
  }

  // Eliminar contrato
  eliminarContrato(idContrato: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/eliminar/${idContrato}/`, this.getHttpOptions());
  }

  // Actualizar ruta de archivo del contrato
  actualizarRutaArchivoContrato(idContrato: number, rutaArchivo: string): Observable<any> {
    const body = { ruta_archivo: rutaArchivo };
    return this.http.put(`${this.apiUrl}/actualizar-archivo/${idContrato}/`, body, this.getHttpOptions());
  }
}
