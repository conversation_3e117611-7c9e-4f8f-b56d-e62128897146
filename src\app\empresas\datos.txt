CREATE TABLE empresa (
    id_empresa INT AUTO_INCREMENT PRIMARY KEY,
    ruc VARCHAR(11) NOT NULL UNIQUE,
    razon_social VARCHAR(255) NOT NULL,
    nombre_comercial VARCHAR(255),
    direccion VARCHAR(255),
    departamento VARCHAR(100),
    provincia VARCHAR(100),
    distrito VARCHAR(100),
    telefono VARCHAR(20),
    correo VARCHAR(100),
    estado CHAR(1) DEFAULT 'A',
    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE representante_legal (
    id_representante INT AUTO_INCREMENT PRIMARY KEY,
    dni VARCHAR(8) NOT NULL UNIQUE,
    nombres VARCHAR(100) NOT NULL,
    apellidos VARCHAR(100) NOT NULL,
    direccion VARCHAR(255),
    telefono VARCHAR(20),
    correo VARCHAR(100),
    cargo VARCHAR(100),
    fecha_cargo DATE,
    estado CHAR(1) DEFAULT 'A',
    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE empresa_representante (
    id_empresa INT,
    id_representante INT,
    tipo_representante VARCHAR(50) DEFAULT 'LEGAL', -- LEGAL, GERENTE, etc.
    PRIMARY KEY (id_empresa, id_representante),
    FOREIGN KEY (id_empresa) REFERENCES empresa(id_empresa),
    FOREIGN KEY (id_representante) REFERENCES representante_legal(id_representante)
);
