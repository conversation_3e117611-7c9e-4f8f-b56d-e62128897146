import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-contract-preview-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './contract-preview-modal.component.html',
  styleUrls: ['./contract-preview-modal.component.scss']
})
export class ContractPreviewModalComponent {
  @Input() htmlContent: string = '';
  @Output() closeModal = new EventEmitter<void>();

  safeHtmlContent: SafeHtml = '';

  constructor(private sanitizer: DomSanitizer) { }

  ngOnChanges(): void {
    this.safeHtmlContent = this.sanitizer.bypassSecurityTrustHtml(this.htmlContent);
  }

  onClose(): void {
    this.closeModal.emit();
  }
}
